[{"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Herd Evaluation'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'FERME O.P. TURGEON' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Health Inspection'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Nutrition Assessment'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'MOO MOUNTAIN MILK INC.'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'MOO MOUNTAIN MILK INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Feed Analysis'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'PARBRO FARMS LTD'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'PARBRO FARMS LTD';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'FERME O.P. TURGEON' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Granjas 4 Irmaos S/a Agropec' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all prospects", "output": "SELECT businessName, country FROM prospect;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all completed visits", "output": "SELECT visitName, visitDate FROM visit WHERE visitStatus = 'completed';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Fazenda VR'", "output": "SELECT contacts FROM customer WHERE businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'Fazenda VR'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda VR';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show favourite prospects", "output": "SELECT businessName FROM prospect WHERE favourite = 1;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'FERME GIRELOU'", "output": "SELECT * FROM customer WHERE businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with milk protein above 3.2%", "output": "SELECT siteName, milkProteinPercent FROM site WHERE milkProteinPercent > 3.2;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'MOO MOUNTAIN MILK INC.' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'MOO MOUNTAIN MILK INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many states are recorded?", "output": "SELECT COUNT(*) FROM states;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find customers with multiple site types", "output": "SELECT c.businessName, COUNT(DISTINCT s.milkingSystemType) as system_types FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.businessName HAVING system_types > 1;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Sítio Barrinha'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Sítio Barrinha';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of animals per customer?", "output": "SELECT c.businessName, SUM(p.animals) as total_animals FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'LMTEST BRAZIL' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show customers in the United States", "output": "SELECT businessName, city, state FROM customer WHERE country = 'United States';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Fazenda São Pedro'", "output": "SELECT * FROM customer WHERE businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find visits with heat stress issues", "output": "SELECT visitName, heatStress FROM visit WHERE heatStress IS NOT NULL;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'FERME GIRELOU'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME GIRELOU';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with excellent milk quality", "output": "SELECT siteName, milkFatPercent, milkProteinPercent FROM site WHERE milkFatPercent > 3.5 AND milkProteinPercent > 3.2;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List sites by state with production totals", "output": "SELECT c.state, COUNT(s.id) as site_count, SUM(s.milk) as total_production FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.state;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What visits were conducted at sites owned by 'Sítio Barrinha'?", "output": "SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'S<PERSON>tio <PERSON>';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'FERME O.P. TURGEON'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'FERME PELLETIER & FILS INC'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show media files with notes", "output": "SELECT m.filename, n.title FROM media m JOIN noteBook n ON m.noteId = n.id;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'Granjas 4 Irmaos S/a Agropec' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'FERME GIRELOU' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Feed Analysis'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many countries are in the database?", "output": "SELECT COUNT(*) FROM countries;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Granjas 4 Irmaos S/a Agropec' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many dairy businesses are registered?", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List image files", "output": "SELECT filename, mediaName FROM media WHERE mediaType = 'image';", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'FERME PELLETIER & FILS INC'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME PELLETIER & FILS INC';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count pens by housing system type", "output": "SELECT housingSystemType, COUNT(*) as pen_count FROM pen GROUP BY housingSystemType;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show video files", "output": "SELECT filename, mediaName FROM media WHERE mediaType = 'video';", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'FERME O.P. TURGEON' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'FERME O.P. TURGEON'", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'FERME RUISSEAU CLAIR' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME RUISSEAU CLAIR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List visits with metabolic incidence data", "output": "SELECT visitName, metabolicIncidence FROM visit WHERE metabolicIncidence IS NOT NULL;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'IAN PETTEY-WEBBVIEW FARMS' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'IAN PETTEY-WEBBVIEW FARMS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'FERME GIRELOU'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Production Review'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Herd <PERSON>'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'BOB MCCOMB-COMBVIEW' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'BOB MCCOMB-COMBVIEW';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites do I have?", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'BOB MCCOMB-COMBVIEW' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'BOB MCCOMB-COMBVIEW';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List logged in users", "output": "SELECT fullName, email FROM users WHERE isLoggedIn = 1;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'FERME DRAPEAU & BELANGER' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME DRAPEAU & BELANGER';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'Fazenda São Pedro'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show diets used at each site", "output": "SELECT s.siteName, d.name as diet_name, d.numberOfAnimals FROM site s JOIN diets d ON s.id = d.siteId;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Fazenda São Pedro'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of animal tags?", "output": "SELECT COUNT(*) FROM earTag;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show notes for each customer", "output": "SELECT c.businessName, n.title, n.note FROM customer c JOIN noteBook n ON c.id = n.accountId;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Production Review'?", "output": "SELECT * FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'FERME DRAPEAU & BELANGER' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME DRAPEAU & BELANGER';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Granjas 4 Irmaos S/a Agropec' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of alerts?", "output": "SELECT COUNT(*) FROM notifications;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'FERME PELLETIER & FILS INC' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many media files are stored?", "output": "SELECT COUNT(*) FROM media;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Sítio Barrinha'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'S<PERSON>tio <PERSON>';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all published visits", "output": "SELECT visitName, visitPublishedDate FROM visit WHERE isVisitAutoPublished = 1;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'IAN PETTEY-WEBBVIEW FARMS'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'IAN PETTEY-WEBBVIEW FARMS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'FERME PELLETIER & FILS INC'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all nutrition plans", "output": "SELECT COUNT(*) FROM diets;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List visits with robotic milk evaluation", "output": "SELECT visitName, roboticMilkEvaluation FROM visit WHERE roboticMilkEvaluation IS NOT NULL;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show facility details for 'FERME O.P. TURGEON'", "output": "SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME O.P. TURGEON';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Granjas 4 Irmaos S/a Agropec'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'FERME GIRELOU' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List ear tags by customer", "output": "SELECT e.earTagName, c.businessName FROM earTag e JOIN customer c ON e.accountId = c.id;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'FERME GIRELOU' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'LMTEST BRAZIL' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all ear tags", "output": "SELECT earTagName FROM earTag;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average dry matter intake across sites?", "output": "SELECT AVG(dryMatterIntake) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many pens does each customer have in total?", "output": "SELECT c.businessName, COUNT(p.id) as total_pens FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'FERME NORVUE'", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME NORVUE';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Granjas 4 Irmaos S/a Agropec'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all farm locations", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show customers with account type 1", "output": "SELECT businessName FROM customer WHERE accountType = 1;", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the average milk production across all sites?", "output": "SELECT AVG(milk) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'FERME GIRELOU'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'BOB MCCOMB-COMBVIEW'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'BOB MCCOMB-COMBVIEW';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show facility details for 'Fazenda VR'", "output": "SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda VR';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List sites with milk other solids above 5.7%", "output": "SELECT siteName, milkOtherSolidsPercent FROM site WHERE milkOtherSolidsPercent > 5.7;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count visits per month by customer", "output": "SELECT c.businessName, strftime('%Y-%m', v.visitDate) as month, COUNT(v.id) as visit_count FROM customer c JOIN site s ON c.id = s.accountId JOIN visit v ON s.id = v.siteId GROUP BY c.businessName, month;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'LMTEST BRAZIL'?", "output": "SELECT accountType FROM customer WHERE businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'FERME GIRELOU'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Production Review'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Feed Analysis'?", "output": "SELECT * FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Which sites have the most pens?", "output": "SELECT siteName, penCount FROM site ORDER BY penCount DESC LIMIT 10;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many ear tags are registered?", "output": "SELECT COUNT(*) FROM earTag;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What visits were conducted at sites owned by 'Fazenda VR'?", "output": "SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda VR';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find notifications by type", "output": "SELECT title, description FROM notifications WHERE type = 'alert';", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show efficiency metrics by region", "output": "SELECT c.state, AVG(s.milk/s.lactatingAnimal) as efficiency FROM customer c JOIN site s ON c.id = s.accountId WHERE s.lactatingAnimal > 0 GROUP BY c.state;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'BOB MCCOMB-COMBVIEW'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'BOB MCCOMB-COMBVIEW';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visit assessments for 'FERME O.P. TURGEON' farms", "output": "SELECT v.visitName, v.rumenHealth, v.locomotionScore, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME O.P. TURGEON';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Production Review'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show users by country", "output": "SELECT fullName, countryId FROM users;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'Fazenda São Pedro'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of sites?", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of generated reports?", "output": "SELECT COUNT(*) FROM visitReport;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List silage by customer", "output": "SELECT s.silageName, c.businessName FROM silage s JOIN customer c ON s.accountId = c.id;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visits with poor locomotion scores", "output": "SELECT visitName, locomotionScore FROM visit WHERE locomotionScore < 2;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all animal types", "output": "SELECT COUNT(*) FROM animalClass;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List reports by visit", "output": "SELECT vr.fileName, v.visitName FROM visitReport vr JOIN visit v ON vr.visitId = v.id;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'LMTEST BRAZIL' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all silage records", "output": "SELECT silageName FROM silage;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all customers in Brazil", "output": "SELECT businessName, city, state FROM customer WHERE country = 'Brazil';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'FERME DRAPEAU & BELANGER'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME DRAPEAU & BELANGER';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'BOB MCCOMB-COMBVIEW'", "output": "SELECT siteCount FROM customer WHERE businessName = 'BOB MCCOMB-COMBVIEW';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'PARBRO FARMS LTD' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'PARBRO FARMS LTD';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Production Review'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me sites with more than 100 parlor stalls", "output": "SELECT siteName, numberOfParlorStalls FROM site WHERE numberOfParlorStalls > 100;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Health Inspection'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of segments?", "output": "SELECT COUNT(*) FROM segment;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Herd Evaluation'?", "output": "SELECT * FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Nutrition Assessment'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show diet details with customer information", "output": "SELECT d.name as diet_name, d.animalType, d.numberOfAnimals, c.businessName FROM diets d JOIN site s ON d.siteId = s.id JOIN customer c ON s.accountId = c.id;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'FERME RUISSEAU CLAIR'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME RUISSEAU CLAIR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Fazenda São Pedro' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'FERME NORVUE'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME NORVUE';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Fazenda VR'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Fazenda VR'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'FERME GIRELOU'?", "output": "SELECT accountType FROM customer WHERE businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visit assessments for 'Fazenda VR' farms", "output": "SELECT v.visitName, v.rumenHealth, v.locomotionScore, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda VR';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of lactating animals across all farms?", "output": "SELECT SUM(lactatingAnimal) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Granjas 4 Irmaos S/a Agropec'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What visits were conducted at sites owned by 'FERME GIRELOU'?", "output": "SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME GIRELOU';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all visits from the last month", "output": "SELECT visitName, visitDate, firstName, lastName FROM visit WHERE visitDate >= date('now', '-1 month');", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'FERME O.P. TURGEON'", "output": "SELECT contacts FROM customer WHERE businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Health Inspection' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Fazenda São Pedro'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Feed Analysis'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average number of lactating animals per site?", "output": "SELECT AVG(lactatingAnimal) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all notifications", "output": "SELECT title, description, type FROM notifications;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'FERME RUISSEAU CLAIR' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME RUISSEAU CLAIR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'FERME PELLETIER & FILS INC' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'FERME NORVUE' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME NORVUE';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'Fazenda VR' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What visits were conducted at sites owned by 'Fazenda São Pedro'?", "output": "SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda São Pedro';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Weekly Health Check'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List unread notifications", "output": "SELECT title, description FROM notifications WHERE isRead = 0;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show contacts with phone numbers", "output": "SELECT fullName, phoneNumber FROM contact WHERE phoneNumber IS NOT NULL;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'DYKEMAN & SONS INC.'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'DYKEMAN & SONS INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What visits were conducted at sites owned by 'FERME O.P. TURGEON'?", "output": "SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME O.P. TURGEON';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all prospect records", "output": "SELECT COUNT(*) FROM prospect;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'FERME NC LAMBERT'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME NC LAMBERT';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all housing pens", "output": "SELECT COUNT(*) FROM pen;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'FERME PELLETIER & FILS INC'", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show customer distribution by country", "output": "SELECT country, COUNT(*) as customer_count FROM customer GROUP BY country;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Nutrition Assessment'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many silage records exist?", "output": "SELECT COUNT(*) FROM silage;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show customers with no sites", "output": "SELECT businessName FROM customer WHERE siteCount = 0;", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Feed Analysis'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'FERME GIRELOU' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Which sites have more than 500 lactating animals?", "output": "SELECT siteName, lactatingAnimal FROM site WHERE lactatingAnimal > 500;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of user accounts?", "output": "SELECT COUNT(*) FROM users;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'FERME O.P. TURGEON' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Granjas 4 Irmaos S/a Agropec' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all visit reports", "output": "SELECT fileName FROM visitReport;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'S<PERSON><PERSON>o Barrinha'", "output": "SELECT * FROM customer WHERE businessName = 'S<PERSON>tio <PERSON>';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'FERME NC LAMBERT' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME NC LAMBERT';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many visit reports exist?", "output": "SELECT COUNT(*) FROM visitReport;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Fazenda VR' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'LMTEST BRAZIL'", "output": "SELECT * FROM customer WHERE businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'DYKEMAN & SONS INC.' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'DYKEMAN & SONS INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'PARBRO FARMS LTD'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'PARBRO FARMS LTD';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Health Inspection'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many contacts are in the system?", "output": "SELECT COUNT(*) FROM contact;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Granjas 4 Irmaos S/a Agropec'", "output": "SELECT contacts FROM customer WHERE businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'LMTEST BRAZIL' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Weekly Health Check'", "output": "SELECT * FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Weekly Health Check'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average ration cost per site?", "output": "SELECT AVG(rationCost) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show facility details for 'Fazenda São Pedro'", "output": "SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda São Pedro';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'FERME O.P. TURGEON'", "output": "SELECT * FROM customer WHERE businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show facility details for 'FERME PELLETIER & FILS INC'", "output": "SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME PELLETIER & FILS INC';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Identify high-maintenance sites", "output": "SELECT s.siteName, COUNT(v.id) as visit_count FROM site s LEFT JOIN visit v ON s.id = v.siteId GROUP BY s.siteName HAVING visit_count > 5;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Which sites have milk fat percentage above 3.5%?", "output": "SELECT siteName, milkFatPercent FROM site WHERE milkFatPercent > 3.5;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many pens are there in total?", "output": "SELECT COUNT(*) FROM pen;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many animal classifications are there?", "output": "SELECT COUNT(*) FROM animalClass;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What are the production metrics for 'Fazenda VR' sites?", "output": "SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda VR';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Fazenda São Pedro'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all contacts", "output": "SELECT fullName, email, phoneNumber FROM contact;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'FERME O.P. TURGEON'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show facility details for 'FERME GIRELOU'", "output": "SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME GIRELOU';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Granjas 4 Irmaos S/a Agropec'", "output": "SELECT * FROM customer WHERE businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show average milk production by country", "output": "SELECT c.country, AVG(s.milk) as avg_production FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.country;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'LMTEST BRAZIL'", "output": "SELECT contacts FROM customer WHERE businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show average milk production per pen by customer", "output": "SELECT c.businessName, AVG(p.milk) as avg_pen_milk FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average number of parlor stalls?", "output": "SELECT AVG(numberOfParlorStalls) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all system notifications", "output": "SELECT COUNT(*) FROM notifications;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'FERME GIRELOU' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List animal classes in French", "output": "SELECT fr_class, fr_subClass FROM animalClass;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Fazenda VR' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'Fazenda VR' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'Fazenda VR'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Weekly Health Check'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all users", "output": "SELECT fullName, email FROM users;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show silage by site", "output": "SELECT s.silageName, st.siteName FROM silage s JOIN site st ON s.siteId = st.id;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Fazenda VR' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find sites with low dry matter intake", "output": "SELECT siteName, dryMatterIntake FROM site WHERE dryMatterIntake < 20;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'FERME NC LAMBERT' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME NC LAMBERT';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many prospects are there?", "output": "SELECT COUNT(*) FROM prospect;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all animal types in English", "output": "SELECT en_class, en_subClass FROM animalClass;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Fazenda São Pedro' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'LMTEST BRAZIL' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the maximum milk production recorded?", "output": "SELECT MAX(milk) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all cities", "output": "SELECT name, countryName FROM cities;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find all robot milking sites", "output": "SELECT siteName FROM site WHERE milkingSystemType = 'Robot';", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'FERME PELLETIER & FILS INC' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find sites with high somatic cell count", "output": "SELECT siteName, milkSomaticCellCount FROM site WHERE milkSomaticCellCount > 200000;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of uploaded files?", "output": "SELECT COUNT(*) FROM media;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List customers in Ontario", "output": "SELECT businessName, city FROM customer WHERE state = 'Ontario';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find contacts with email addresses", "output": "SELECT fullName, email FROM contact WHERE email IS NOT NULL;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'FERME PELLETIER & FILS INC' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Fazenda VR' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'FERME PELLETIER & FILS INC' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'LAGGIS BROTHERS' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'LAGGIS BROTHERS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all visits by <PERSON>", "output": "SELECT visitName, visitDate FROM visit WHERE firstName = '<PERSON>' AND lastName = '<PERSON>';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all system users", "output": "SELECT COUNT(*) FROM users;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Herd Evaluation'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Herd Evaluation' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for '<PERSON><PERSON><PERSON><PERSON>'", "output": "SELECT contacts FROM customer WHERE businessName = 'Sítio <PERSON>';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'FERME RUISSEAU CLAIR'", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME RUISSEAU CLAIR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show pen details with site information", "output": "SELECT p.name as pen_name, p.animals, s.siteName, c.businessName FROM pen p JOIN site s ON p.siteId = s.id JOIN customer c ON s.accountId = c.id;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with best milk quality scores", "output": "SELECT siteName, milkFatPercent, milkProteinPercent, milkSomaticCellCount FROM site WHERE milkFatPercent > 3.5 AND milkProteinPercent > 3.2 AND milkSomaticCellCount < 200000;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show correlation between parlor stalls and production", "output": "SELECT numberOfParlorStalls, AVG(milk) as avg_production FROM site GROUP BY numberOfParlorStalls;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'FERME GIRELOU'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'PARBRO FARMS LTD'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'PARBRO FARMS LTD';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List prospects by country", "output": "SELECT businessName FROM prospect WHERE country = 'Canada';", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all farm notes", "output": "SELECT COUNT(*) FROM noteBook;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all countries", "output": "SELECT name, countryCode FROM countries;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of silage entries?", "output": "SELECT COUNT(*) FROM silage;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'Sítio Barrinha'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = '<PERSON><PERSON><PERSON><PERSON>';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'LMTEST BRAZIL'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'IAN PETTEY-WEBBVIEW FARMS' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'IAN PETTEY-WEBBVIEW FARMS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'PARBRO FARMS LTD' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'PARBRO FARMS LTD';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Fazenda VR' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'FERME DRAPEAU & BELANGER'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME DRAPEAU & BELANGER';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of animal categories?", "output": "SELECT COUNT(*) FROM animalClass;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average milk protein percentage?", "output": "SELECT AVG(milkProteinPercent) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show ear tags by site", "output": "SELECT e.earTagName, s.siteName FROM earTag e JOIN site s ON e.siteId = s.id;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'FERME RUISSEAU CLAIR'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME RUISSEAU CLAIR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of '<PERSON><PERSON><PERSON><PERSON> Barr<PERSON>ha'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Sítio Barrinha';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'MOO MOUNTAIN MILK INC.'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'MOO MOUNTAIN MILK INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'FERME RUISSEAU CLAIR' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME RUISSEAU CLAIR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'FERME PELLETIER & FILS INC'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Fazenda VR'", "output": "SELECT * FROM customer WHERE businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'Fazenda São Pedro'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda São Pedro';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Nutrition Assessment'", "output": "SELECT * FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find heifer classifications", "output": "SELECT className, en_class FROM animalClass WHERE en_class LIKE '%heifer%';", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average milk fat percentage?", "output": "SELECT AVG(milkFatPercent) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was '<PERSON><PERSON><PERSON><PERSON>' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Sítio Barrinha';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Sítio Barrinha' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Sítio Barrinha';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total milk production from all sites?", "output": "SELECT SUM(milk) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all ear tag records", "output": "SELECT COUNT(*) FROM earTag;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'FERME GIRELOU' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What are the production metrics for 'Fazenda São Pedro' sites?", "output": "SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda São Pedro';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of '<PERSON><PERSON><PERSON><PERSON>'?", "output": "SELECT accountType FROM customer WHERE businessName = 'S<PERSON>tio <PERSON>';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What are the production metrics for 'FERME O.P. TURGEON' sites?", "output": "SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME O.P. TURGEON';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all silage storage records", "output": "SELECT COUNT(*) FROM silage;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Fazenda VR'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'FERME O.P. TURGEON'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'FERME O.P. TURGEON'?", "output": "SELECT accountType FROM customer WHERE businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'FERME O.P. TURGEON' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of feeding programs?", "output": "SELECT COUNT(*) FROM diets;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'FERME DRAPEAU & BELANGER'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME DRAPEAU & BELANGER';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'LAGGIS BROTHERS' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'LAGGIS BROTHERS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Fazenda São Pedro' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'FERME PELLETIER & FILS INC'", "output": "SELECT contacts FROM customer WHERE businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many documentation entries exist?", "output": "SELECT COUNT(*) FROM noteBook;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find customers in São Paulo", "output": "SELECT businessName, city FROM customer WHERE state = 'São Paulo';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show facility details for '<PERSON><PERSON><PERSON>o Barrinha'", "output": "SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Sítio Barrinha';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Health Inspection'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List lactating animal categories", "output": "SELECT className, en_class FROM animalClass WHERE en_class LIKE '%lactating%';", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show dry cow classifications", "output": "SELECT className, en_class FROM animalClass WHERE en_class LIKE '%dry%';", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'FERME DRAPEAU & BELANGER'", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME DRAPEAU & BELANGER';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find visits with manure screening results", "output": "SELECT visitName, manureScreener FROM visit WHERE manureScreener IS NOT NULL;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'Fazenda VR'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find sites with declining performance", "output": "SELECT siteName, milk, milkFatPercent FROM site WHERE milk < (SELECT AVG(milk) FROM site);", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'BOB MCCOMB-COMBVIEW' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'BOB MCCOMB-COMBVIEW';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many notes have been recorded?", "output": "SELECT COUNT(*) FROM noteBook;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show synced reports", "output": "SELECT fileName FROM visitReport WHERE is_synced = 1;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'FERME DRAPEAU & BELANGER' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME DRAPEAU & BELANGER';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'FERME NC LAMBERT'", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME NC LAMBERT';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all segments", "output": "SELECT name, defaultValue FROM segment;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all states", "output": "SELECT name, stateCode FROM states;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Weekly Health Check'?", "output": "SELECT * FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Health Inspection'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'PARBRO FARMS LTD' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'PARBRO FARMS LTD';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'BOB MCCOMB-COMBVIEW'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'BOB MCCOMB-COMBVIEW';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Nutrition Assessment'?", "output": "SELECT * FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Granjas 4 Irmaos S/a Agropec'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many visits have been conducted?", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'Granjas 4 Irmaos S/a Agropec'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'LAGGIS BROTHERS' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'LAGGIS BROTHERS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Feed Analysis'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many diets are configured?", "output": "SELECT COUNT(*) FROM diets;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find customers with more than 5 sites", "output": "SELECT businessName, siteCount FROM customer WHERE siteCount > 5;", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'FERME NC LAMBERT'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME NC LAMBERT';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Weekly Health Check'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'FERME NORVUE'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME NORVUE';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visits with forage audit scorecards", "output": "SELECT visitName, forageAuditScorecard FROM visit WHERE forageAuditScorecard IS NOT NULL;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'LAGGIS BROTHERS'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'LAGGIS BROTHERS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'Fazenda São Pedro' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many site inspections are recorded?", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List customers by segment", "output": "SELECT c.businessName, s.name as segment FROM customer c JOIN segment s ON c.segmentId = s.id;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'PARBRO FARMS LTD' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'PARBRO FARMS LTD';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Health Inspection'", "output": "SELECT * FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count notes per site", "output": "SELECT s.siteName, COUNT(n.id) as note_count FROM site s LEFT JOIN noteBook n ON s.id = n.siteId GROUP BY s.siteName;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many animal enclosures exist?", "output": "SELECT COUNT(*) FROM pen;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'FERME O.P. TURGEON' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Feed Analysis' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'FERME PELLETIER & FILS INC'", "output": "SELECT * FROM customer WHERE businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show top 10 performing sites by milk production", "output": "SELECT siteName, milk FROM site ORDER BY milk DESC LIMIT 10;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'DYKEMAN & SONS INC.'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'DYKEMAN & SONS INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with milk price above 150", "output": "SELECT siteName, currentMilkPrice FROM site WHERE currentMilkPrice > 150;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'LAGGIS BROTHERS'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'LAGGIS BROTHERS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visit assessments for 'FERME PELLETIER & FILS INC' farms", "output": "SELECT v.visitName, v.rumenHealth, v.locomotionScore, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME PELLETIER & FILS INC';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the milk production range", "output": "SELECT MIN(milk) as min_production, MAX(milk) as max_production FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'Sítio Barrinha' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'S<PERSON>tio Barrinha';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all media files", "output": "SELECT filename, mediaName, mediaType FROM media;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'MOO MOUNTAIN MILK INC.' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'MOO MOUNTAIN MILK INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many notifications exist?", "output": "SELECT COUNT(*) FROM notifications;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Nutrition Assessment' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find media files by visit", "output": "SELECT m.filename, v.visitName FROM media m JOIN visit v ON m.visitId = v.id;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'DYKEMAN & SONS INC.' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'DYKEMAN & SONS INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'FERME O.P. TURGEON' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show German animal classifications", "output": "SELECT de_class, de_subClass FROM animalClass;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show read notifications", "output": "SELECT title, description FROM notifications WHERE isRead = 1;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of animal pens?", "output": "SELECT COUNT(*) FROM pen;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find sites needing attention", "output": "SELECT siteName, milkSomaticCellCount, bacteriaCellCount FROM site WHERE milkSomaticCellCount > 400000 OR bacteriaCellCount > 20000;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with tie stall systems", "output": "SELECT siteName FROM site WHERE milkingSystemType = 'Tie <PERSON>all';", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many customers do we have?", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'MOO MOUNTAIN MILK INC.' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'MOO MOUNTAIN MILK INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many users are in the system?", "output": "SELECT COUNT(*) FROM users;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all media items", "output": "SELECT COUNT(*) FROM media;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Fazenda São Pedro'", "output": "SELECT contacts FROM customer WHERE businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'Granjas 4 Irmaos S/a Agropec' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'FERME O.P. TURGEON'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME O.P. TURGEON';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'FERME NC LAMBERT' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME NC LAMBERT';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'DYKEMAN & SONS INC.'", "output": "SELECT siteCount FROM customer WHERE businessName = 'DYKEMAN & SONS INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'LAGGIS BROTHERS' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'LAGGIS BROTHERS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find sites with less than 100 lactating animals", "output": "SELECT siteName, lactatingAnimal FROM site WHERE lactatingAnimal < 100;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all contact names", "output": "SELECT fullName FROM contact;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of observations?", "output": "SELECT COUNT(*) FROM noteBook;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find users by email domain", "output": "SELECT fullName, email FROM users WHERE email LIKE '%@example.com';", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'LAGGIS BROTHERS'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'LAGGIS BROTHERS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show feeding system distribution", "output": "SELECT feedingSystemType, COUNT(*) as pen_count FROM pen GROUP BY feedingSystemType;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Granjas 4 Irmaos S/a Agropec'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'FERME PELLETIER & FILS INC' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Fazenda São Pedro'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'FERME PELLETIER & FILS INC'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'DYKEMAN & SONS INC.' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'DYKEMAN & SONS INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What are the production metrics for 'Sítio Barrinha' sites?", "output": "SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Sítio Barr<PERSON>ha';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Herd Evaluation'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Fazenda São Pedro' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'Granjas 4 Irmaos S/a Agropec'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for '<PERSON><PERSON><PERSON><PERSON> Barrinha'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'S<PERSON>tio Barrinha';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of contact records?", "output": "SELECT COUNT(*) FROM contact;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'FERME NORVUE' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME NORVUE';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Herd Evaluation'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Which customers use the most diet programs?", "output": "SELECT c.businessName, COUNT(d.id) as diet_count FROM customer c JOIN site s ON c.id = s.accountId JOIN diets d ON s.id = d.siteId GROUP BY c.businessName ORDER BY diet_count DESC;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'FERME GIRELOU'", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'LAGGIS BROTHERS'", "output": "SELECT siteCount FROM customer WHERE businessName = 'LAGGIS BROTHERS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What visits were conducted at sites owned by 'FERME PELLETIER & FILS INC'?", "output": "SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME PELLETIER & FILS INC';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show favourite notes by customer", "output": "SELECT c.businessName, n.title FROM customer c JOIN noteBook n ON c.id = n.accountId WHERE n.favourite = 1;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is '<PERSON><PERSON><PERSON><PERSON>' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = '<PERSON><PERSON><PERSON><PERSON>';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Nutrition Assessment'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'LMTEST BRAZIL'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'IAN PETTEY-WEBBVIEW FARMS' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'IAN PETTEY-WEBBVIEW FARMS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'FERME GIRELOU'", "output": "SELECT contacts FROM customer WHERE businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'Fazenda São Pedro' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'LMTEST BRAZIL' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Health Inspection'?", "output": "SELECT * FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all animal classifications", "output": "SELECT className, en_class, en_subClass FROM animalClass;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with low bacteria cell count", "output": "SELECT siteName, bacteriaCellCount FROM site WHERE bacteriaCellCount < 10000;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all customer accounts", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'MOO MOUNTAIN MILK INC.'", "output": "SELECT siteCount FROM customer WHERE businessName = 'MOO MOUNTAIN MILK INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Weekly Health Check'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Fazenda VR'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'FERME PELLETIER & FILS INC'?", "output": "SELECT accountType FROM customer WHERE businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'FERME PELLETIER & FILS INC' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find visits with high rumen health scores", "output": "SELECT visitName, rumenHealth FROM visit WHERE rumenHealth > 3;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visits with calf heifer scorecard", "output": "SELECT visitName, calfHeiferScorecard FROM visit WHERE calfHeiferScorecard IS NOT NULL;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'Sítio Barrinha'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'S<PERSON>tio Barrinha';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'FERME O.P. TURGEON'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'IAN PETTEY-WEBBVIEW FARMS'", "output": "SELECT siteCount FROM customer WHERE businessName = 'IAN PETTEY-WEBBVIEW FARMS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visit assessments for 'Fazenda São Pedro' farms", "output": "SELECT v.visitName, v.rumenHealth, v.locomotionScore, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Fazenda São Pedro';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the net energy levels across sites", "output": "SELECT siteName, netEnergyOfLactationDairy FROM site WHERE netEnergyOfLactationDairy IS NOT NULL;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show customers in Quebec", "output": "SELECT businessName, city FROM customer WHERE state = 'Quebec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'FERME NORVUE' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME NORVUE';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Fazenda São Pedro'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'FERME GIRELOU'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'FERME NC LAMBERT' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME NC LAMBERT';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all customers in Canada", "output": "SELECT businessName, city, state FROM customer WHERE country = 'Canada';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'LMTEST BRAZIL'", "output": "SELECT siteCount FROM customer WHERE businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'FERME O.P. TURGEON'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'FERME O.P. TURGEON';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'LMTEST BRAZIL'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Fazenda São Pedro' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Fazenda São Pedro';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'FERME RUISSEAU CLAIR'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME RUISSEAU CLAIR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the average current milk price?", "output": "SELECT AVG(currentMilkPrice) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'MOO MOUNTAIN MILK INC.' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'MOO MOUNTAIN MILK INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Sítio Barrinha' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Sítio Barrinha';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What are the production metrics for 'FERME PELLETIER & FILS INC' sites?", "output": "SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME PELLETIER & FILS INC';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'Sítio Barrinha' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'S<PERSON>tio Barrinha';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'FERME RUISSEAU CLAIR' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME RUISSEAU CLAIR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visits with TMR particle scores", "output": "SELECT visitName, tmrParticleScore FROM visit WHERE tmrParticleScore IS NOT NULL;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'IAN PETTEY-WEBBVIEW FARMS'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'IAN PETTEY-WEBBVIEW FARMS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'FERME NORVUE' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME NORVUE';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Nutrition Assessment'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'BOB MCCOMB-COMBVIEW' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'BOB MCCOMB-COMBVIEW';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find visits with profitability analysis", "output": "SELECT visitName, profitabilityAnalysis FROM visit WHERE profitabilityAnalysis IS NOT NULL;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Weekly Health Check' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of accounts?", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Production Review'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'FERME PELLETIER & FILS INC'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME PELLETIER & FILS INC';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Production Review'", "output": "SELECT * FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Granjas 4 Irmaos S/a Agropec' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Granjas 4 Irmaos S/a Agropec';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Identify underperforming pens", "output": "SELECT name, milk/animals as efficiency FROM pen WHERE animals > 0 ORDER BY efficiency ASC LIMIT 10;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with days in milk above 200", "output": "SELECT siteName, daysInMilk FROM site WHERE daysInMilk > 200;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'IAN PETTEY-WEBBVIEW FARMS'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'IAN PETTEY-WEBBVIEW FARMS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visit assessments for 'Sítio Barrinha' farms", "output": "SELECT v.visitName, v.rumenHealth, v.locomotionScore, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Sítio Barr<PERSON>ha';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all favourite customers", "output": "SELECT businessName FROM customer WHERE favourite = 1;", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many feed formulations exist?", "output": "SELECT COUNT(*) FROM diets;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'FERME NORVUE'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'FERME NORVUE';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'IAN PETTEY-WEBBVIEW FARMS' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'IAN PETTEY-WEBBVIEW FARMS';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all farm assessments", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Feed Analysis'", "output": "SELECT * FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show states by country", "output": "SELECT s.name, c.name as country FROM states s JOIN countries c ON s.countryCode = c.countryCode;", "category": "all_tables"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show notes with visit information", "output": "SELECT n.title, n.note, v.visitName, v.visitDate FROM noteBook n JOIN visit v ON n.visitId = v.id;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all site names for 'LMTEST BRAZIL'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with high as-fed intake", "output": "SELECT siteName, asFedIntake FROM site WHERE asFedIntake > 50;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of farm visits?", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'LMTEST BRAZIL' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'MOO MOUNTAIN MILK INC.'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'MOO MOUNTAIN MILK INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Give me name of all the sites customer 'FERME GIRELOU' has", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME GIRELOU';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many dairy farms are in the database?", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find sites with ration cost above 5", "output": "SELECT siteName, rationCost FROM site WHERE rationCost > 5.0;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find sites with milk price below 50", "output": "SELECT siteName, currentMilkPrice FROM site WHERE currentMilkPrice < 50;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Sítio Barrinha' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Sítio Barrinha';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visit assessments for 'FERME GIRELOU' farms", "output": "SELECT v.visitName, v.rumenHealth, v.locomotionScore, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME GIRELOU';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Fazenda VR'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Fazenda VR';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average days in milk for all sites?", "output": "SELECT AVG(daysInMilk) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show active diets by animal type", "output": "SELECT animalType, COUNT(*) as diet_count FROM diets WHERE isActive = 1 GROUP BY animalType;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many cities are in the system?", "output": "SELECT COUNT(*) FROM cities;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with more than 20 pens", "output": "SELECT siteName, penCount FROM site WHERE penCount > 20;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'DYKEMAN & SONS INC.'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'DYKEMAN & SONS INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the minimum milk production recorded?", "output": "SELECT MIN(milk) FROM site;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What are the production metrics for 'FERME GIRELOU' sites?", "output": "SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME GIRELOU';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all visit report files", "output": "SELECT COUNT(*) FROM visitReport;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Herd Evaluation'", "output": "SELECT * FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List sites by milking system type", "output": "SELECT milkingSystemType, COUNT(*) as site_count FROM site GROUP BY milkingSystemType;", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'LMTEST BRAZIL'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'LMTEST BRAZIL';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of potential customers?", "output": "SELECT COUNT(*) FROM prospect;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all parlor milking sites", "output": "SELECT siteName FROM site WHERE milkingSystemType = 'Parlor';", "category": "site_production"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all contact entries", "output": "SELECT COUNT(*) FROM contact;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all farm locations for 'FERME NC LAMBERT'", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME NC LAMBERT';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Sítio Barrinha'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Sítio Barrinha';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show recent visits with site information", "output": "SELECT v.visitName, v.visitDate, s.siteName, c.businessName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE v.visitDate >= date('now', '-30 days');", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Production Review' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'DYKEMAN & SONS INC.' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'DYKEMAN & SONS INC.';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'PARBRO FARMS LTD'", "output": "SELECT siteCount FROM customer WHERE businessName = 'PARBRO FARMS LTD';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What sites does 'FERME DRAPEAU & BELANGER' own?", "output": "SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'FERME DRAPEAU & BELANGER';", "category": "customer_specific"}]