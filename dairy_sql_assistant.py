#!/usr/bin/env python3
"""
Dairy SQL Assistant - Inference Script
Uses the fine-tuned model to generate SQL queries from natural language questions
"""

import torch
from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
import sqlite3
import pandas as pd
from typing import List, Dict, Optional
import json
import os

class DairySQLAssistant:
    def __init__(self, model_path: str = "./dairy-sql-model", db_path: str = None):
        """
        Initialize the Dairy SQL Assistant
        
        Args:
            model_path: Path to the fine-tuned model
            db_path: Path to the SQLite database file
        """
        self.model_path = model_path
        self.db_path = db_path
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.tokenizer = None
        self.model = None
        self.db_connection = None
        
        print(f"Using device: {self.device}")
        self.load_model()
        
        if db_path and os.path.exists(db_path):
            self.connect_database()
    
    def load_model(self):
        """Load the fine-tuned model and tokenizer"""
        try:
            print(f"Loading model from: {self.model_path}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = AutoModelForSeq2SeqLM.from_pretrained(self.model_path)
            self.model.to(self.device)
            self.model.eval()
            print("Model loaded successfully!")
        except Exception as e:
            print(f"Error loading model: {e}")
            print("Falling back to base model...")
            self.load_base_model()
    
    def load_base_model(self):
        """Load the base CodeT5+ model if fine-tuned model is not available"""
        model_name = "Salesforce/codet5p-220m"
        print(f"Loading base model: {model_name}")
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForSeq2SeqLM.from_pretrained(model_name)
        self.model.to(self.device)
        self.model.eval()
        print("Base model loaded successfully!")
    
    def connect_database(self):
        """Connect to the SQLite database"""
        try:
            self.db_connection = sqlite3.connect(self.db_path)
            print(f"Connected to database: {self.db_path}")
        except Exception as e:
            print(f"Error connecting to database: {e}")
    
    def generate_sql(self, question: str, temperature: float = 0.7, max_length: int = 256) -> str:
        """
        Generate SQL query from natural language question
        
        Args:
            question: Natural language question
            temperature: Sampling temperature (0.0 = deterministic, 1.0 = random)
            max_length: Maximum length of generated SQL
            
        Returns:
            Generated SQL query
        """
        # Prepare input text
        input_text = f"Generate SQL query for the following question about dairy farm data:\nQuestion: {question}\nSQL:"
        
        # Tokenize input
        inputs = self.tokenizer(
            input_text, 
            return_tensors="pt", 
            max_length=512, 
            truncation=True,
            padding=True
        )
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Generate SQL
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_length=max_length,
                num_beams=4,
                temperature=temperature,
                do_sample=True if temperature > 0 else False,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                early_stopping=True
            )
        
        # Decode generated SQL
        generated_sql = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Clean up the generated SQL
        generated_sql = self.clean_sql(generated_sql)
        
        return generated_sql
    
    def clean_sql(self, sql: str) -> str:
        """Clean and format the generated SQL"""
        # Remove the input prompt if it appears in output
        if "SQL:" in sql:
            sql = sql.split("SQL:")[-1].strip()
        
        # Remove extra whitespace
        sql = " ".join(sql.split())
        
        # Ensure SQL ends with semicolon
        if not sql.endswith(';'):
            sql += ';'
        
        return sql
    
    def execute_sql(self, sql: str) -> Optional[pd.DataFrame]:
        """
        Execute SQL query on the database
        
        Args:
            sql: SQL query to execute
            
        Returns:
            Query results as pandas DataFrame or None if error
        """
        if not self.db_connection:
            print("No database connection available")
            return None
        
        try:
            result = pd.read_sql_query(sql, self.db_connection)
            return result
        except Exception as e:
            print(f"Error executing SQL: {e}")
            return None
    
    def ask_question(self, question: str, execute: bool = True) -> Dict:
        """
        Ask a question and get both SQL and results
        
        Args:
            question: Natural language question
            execute: Whether to execute the SQL on database
            
        Returns:
            Dictionary with question, generated SQL, and results
        """
        print(f"\n🤔 Question: {question}")
        
        # Generate SQL
        sql = self.generate_sql(question)
        print(f"🔍 Generated SQL: {sql}")
        
        result = {
            'question': question,
            'sql': sql,
            'results': None,
            'error': None
        }
        
        # Execute SQL if requested and database is available
        if execute and self.db_connection:
            try:
                results = self.execute_sql(sql)
                if results is not None:
                    result['results'] = results
                    print(f"✅ Results ({len(results)} rows):")
                    print(results.to_string(index=False))
                else:
                    result['error'] = "Failed to execute SQL"
                    print("❌ Failed to execute SQL")
            except Exception as e:
                result['error'] = str(e)
                print(f"❌ Error: {e}")
        
        return result
    
    def interactive_mode(self):
        """Start interactive question-answering mode"""
        print("\n" + "="*60)
        print("🐄 DAIRY SQL ASSISTANT - Interactive Mode")
        print("="*60)
        print("Ask questions about your dairy farm data!")
        print("Type 'quit' or 'exit' to stop")
        print("Type 'help' for example questions")
        print("-"*60)
        
        while True:
            try:
                question = input("\n💬 Your question: ").strip()
                
                if question.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if question.lower() == 'help':
                    self.show_help()
                    continue
                
                if not question:
                    continue
                
                # Process the question
                self.ask_question(question)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def show_help(self):
        """Show example questions"""
        examples = [
            "How many sites do I have?",
            "How many sites does Sunrise Dairy Farm have?",
            "What data is inside visit 'Health Check'?",
            "What is the average milk production across all sites?",
            "Which sites have milk fat percentage above 3.5%?",
            "Show me all customers in Canada",
            "What visits were conducted last month?",
            "How many lactating animals do we have in total?"
        ]
        
        print("\n📝 Example questions you can ask:")
        print("-" * 40)
        for i, example in enumerate(examples, 1):
            print(f"{i}. {example}")
    
    def batch_questions(self, questions: List[str], save_results: bool = True) -> List[Dict]:
        """
        Process multiple questions in batch
        
        Args:
            questions: List of questions to process
            save_results: Whether to save results to JSON file
            
        Returns:
            List of results for each question
        """
        results = []
        
        print(f"\n🔄 Processing {len(questions)} questions...")
        
        for i, question in enumerate(questions, 1):
            print(f"\n[{i}/{len(questions)}]")
            result = self.ask_question(question)
            results.append(result)
        
        if save_results:
            output_file = "dairy_sql_results.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            print(f"\n💾 Results saved to: {output_file}")
        
        return results

def main():
    """Main function to run the assistant"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Dairy SQL Assistant")
    parser.add_argument("--model", default="./dairy-sql-model", help="Path to fine-tuned model")
    parser.add_argument("--database", help="Path to SQLite database file")
    parser.add_argument("--interactive", action="store_true", help="Start interactive mode")
    parser.add_argument("--question", help="Single question to ask")
    
    args = parser.parse_args()
    
    # Initialize assistant
    assistant = DairySQLAssistant(
        model_path=args.model,
        db_path=args.database
    )
    
    if args.interactive:
        # Interactive mode
        assistant.interactive_mode()
    elif args.question:
        # Single question
        assistant.ask_question(args.question)
    else:
        # Demo mode with example questions
        demo_questions = [
            "How many sites do I have?",
            "How many sites does Sunrise Dairy Farm have?",
            "What is the average milk production?",
            "Show me all customers in Canada"
        ]
        
        print("🚀 Running demo with example questions...")
        assistant.batch_questions(demo_questions)

if __name__ == "__main__":
    main()
