#!/usr/bin/env python3
"""
COMPREHENSIVE Dairy Database Training Dataset Generator
Includes ALL tables from the Cargill dairy database schema
Based on real data patterns and relationships from the SQL file
"""

import json
import random
from typing import List, Dict, Tuple
from datetime import datetime, timedelta
import itertools

class ComprehensiveDairyDatasetGenerator:
    def __init__(self):
        # Complete database schema with ALL tables and relationships
        self.tables = {
            'animalClass': {
                'columns': ['id', 'className', 'de_class', 'de_subClass', 'en_class', 'en_subClass', 'fr_class', 'fr_subClass'],
                'description': 'Animal classifications with multilingual support',
                'sample_data': ['lactatingFresh', 'lactatingMilking', 'dryFarOff', 'dryCloseUp', 'heifer', 'calf']
            },
            'cities': {
                'columns': ['id', 'name', 'countryCode', 'countryName', 'stateCode', 'stateName'],
                'description': 'City reference data with geographic information'
            },
            'contact': {
                'columns': ['id', 'sfdcId', 'fullName', 'email', 'phoneNumber'],
                'description': 'Contact information for customers and users'
            },
            'countries': {
                'columns': ['id', 'name', 'countryCode', 'countryBusinessIdMapping', 'countryKey'],
                'description': 'Country reference data with business mappings'
            },
            'customer': {
                'columns': ['id', 'businessName', 'customerCode', 'accountType', 'type', 'country', 'street', 'state', 'city', 'postalCode', 'segmentId', 'contacts', 'favourite', 'siteCount', 'dateOfLastVisit', 'updated_at', 'userRoles', 'users', 'accessIdentifier'],
                'description': 'Customer/account information including dairy farms and businesses',
                'relationships': {'sites': 'customer.id = site.accountId'}
            },
            'diets': {
                'columns': ['id', 'animalType', 'animalTypeId', 'barnId', 'breedId', 'breedName', 'isActive', 'isDeleted', 'name', 'numberOfAnimals', 'siteId', 'source', 'analyzeOptimization', 'formulateOptimization', 'updated_at'],
                'description': 'Feed diets and nutrition programs for animals',
                'relationships': {'site': 'diets.siteId = site.id'}
            },
            'earTag': {
                'columns': ['id', 'earTagName', 'accountId', 'siteId', 'accessIdentifier'],
                'description': 'Animal ear tag identification system',
                'relationships': {'customer': 'earTag.accountId = customer.id', 'site': 'earTag.siteId = site.id'}
            },
            'media': {
                'columns': ['id', 'filename', 'mediaName', 'mediaType', 'mime', 'noteId', 'path', 'reportType', 'accountId', 'visitId', 'userId', 'deleted'],
                'description': 'Media files associated with visits and notes',
                'relationships': {'customer': 'media.accountId = customer.id', 'visit': 'media.visitId = visit.id', 'noteBook': 'media.noteId = noteBook.id'}
            },
            'noteBook': {
                'columns': ['id', 'title', 'note', 'accountId', 'siteId', 'visitId', 'section', 'sectionId', 'sectionTitle', 'category', 'createUser', 'lastModifyUser', 'deleted', 'mediaItems', 'favourite', 'createdDate', 'updatedDate', 'accessIdentifier'],
                'description': 'Notes and observations from farm visits',
                'relationships': {'customer': 'noteBook.accountId = customer.id', 'site': 'noteBook.siteId = site.id', 'visit': 'noteBook.visitId = visit.id'}
            },
            'notifications': {
                'columns': ['id', 'visitId', 'noteId', 'title', 'description', 'type', 'isRead', 'userId', 'deleted', 'scheduleTime', 'updatedDate', 'createdDate'],
                'description': 'System notifications for users',
                'relationships': {'visit': 'notifications.visitId = visit.id', 'noteBook': 'notifications.noteId = noteBook.id'}
            },
            'pen': {
                'columns': ['id', 'accountId', 'siteId', 'barnId', 'source', 'name', 'barnName', 'housingSystemType', 'feedingSystemType', 'numberOfStalls', 'milkingFrequency', 'animals', 'daysInMilk', 'milk', 'dietId', 'animalClassId', 'dryMatterIntake', 'asFedIntake', 'rationCostPerAnimal', 'isDeleted', 'createUser', 'updatedDate', 'accessIdentifier'],
                'description': 'Animal housing pens within dairy farms with detailed metrics',
                'relationships': {'customer': 'pen.accountId = customer.id', 'site': 'pen.siteId = site.id', 'diets': 'pen.dietId = diets.id', 'animalClass': 'pen.animalClassId = animalClass.id'}
            },
            'prospect': {
                'columns': ['id', 'businessName', 'customerCode', 'accountType', 'type', 'country', 'street', 'state', 'city', 'postalCode', 'segmentId', 'contacts', 'favourite', 'siteCount', 'dateOfLastVisit'],
                'description': 'Prospective customers and leads'
            },
            'segment': {
                'columns': ['id', 'name', 'defaultValue'],
                'description': 'Customer segmentation data',
                'relationships': {'customer': 'customer.segmentId = segment.id'}
            },
            'silage': {
                'columns': ['id', 'silageName', 'accountId', 'siteId', 'updated', 'accessIdentifier'],
                'description': 'Silage feed storage information',
                'relationships': {'customer': 'silage.accountId = customer.id', 'site': 'silage.siteId = site.id'}
            },
            'site': {
                'columns': ['id', 'accountId', 'siteName', 'currentMilkPrice', 'milkingSystemType', 'numberOfParlorStalls', 'lactatingAnimal', 'daysInMilk', 'milk', 'milkFatPercent', 'milkProteinPercent', 'milkOtherSolidsPercent', 'milkSomaticCellCount', 'bacteriaCellCount', 'dryMatterIntake', 'asFedIntake', 'netEnergyOfLactationDairy', 'rationCost', 'hasReport', 'origination', 'penCount', 'dateOfLastVisit', 'updated_at', 'keys', 'updated', 'syncError', 'hasChildError', 'accessIdentifier'],
                'description': 'Individual dairy farm sites with comprehensive production metrics',
                'relationships': {'customer': 'site.accountId = customer.id'}
            },
            'states': {
                'columns': ['id', 'name', 'stateCode', 'countryCode', 'stateKey'],
                'description': 'State/province reference data'
            },
            'users': {
                'columns': ['id', 'guid', 'accountType', 'authenticationPlatform', 'email', 'fullName', 'mobileNumber', 'password', 'principalName', 'countryId', 'userPreferences', 'created_at', 'updated_at', 'isLoggedIn'],
                'description': 'System users and authentication data'
            },
            'visit': {
                'columns': ['id', 'customerId', 'siteId', 'visitDate', 'firstName', 'lastName', 'visitStatus', 'selected', 'visitName', 'isVisitAutoPublished', 'rumenHealth', 'locomotionScore', 'rumenHealthManureScore', 'foragePennState', 'penTimeBudgetTool', 'rumenFill', 'bodyCondition', 'animalAnalysis', 'milkSoldEvaluation', 'metabolicIncidence', 'roboticMilkEvaluation', 'pileAndBunker', 'tmrParticleScore', 'manureScreener', 'forageAuditScorecard', 'heatStress', 'profitabilityAnalysis', 'calfHeiferScorecard', 'selectedCurrency', 'unitOfMeasure', 'selectedPointScale', 'visitPublishedDate', 'createUser', 'lastModifyUser', 'updated_at', 'mobileLastUpdatedTime', 'accessIdentifier', 'usedPens', 'deleted'],
                'description': 'Comprehensive farm visits and assessments by specialists',
                'relationships': {'customer': 'visit.customerId = customer.id', 'site': 'visit.siteId = site.id'}
            },
            'visitReport': {
                'columns': ['id', 'visitId', 'fileName', 'fileId', 'accessIdentifier', 'created_at', 'is_synced'],
                'description': 'Reports generated from farm visits',
                'relationships': {'visit': 'visitReport.visitId = visit.id'}
            }
        }
        
        # Real data from your schema
        self.real_customers = [
            'FERME O.P. TURGEON', 'Fazenda VR', 'FERME PELLETIER & FILS INC', 'Fazenda São Pedro',
            'Sítio Barrinha', 'FERME GIRELOU', 'Granjas 4 Irmaos S/a Agropec', 'LMTEST BRAZIL',
            'IAN PETTEY-WEBBVIEW FARMS', 'FERME RUISSEAU CLAIR', 'FERME NORVUE', 'FERME NC LAMBERT',
            'PARBRO FARMS LTD', 'BOB MCCOMB-COMBVIEW', 'LAGGIS BROTHERS', 'MOO MOUNTAIN MILK INC.',
            'FERME DRAPEAU & BELANGER', 'DYKEMAN & SONS INC.'
        ]
        
        self.real_countries = ['Canada', 'Brazil', 'United States']
        self.real_states = ['Quebec', 'Ontario', 'Goiás', 'Minas Gerais', 'Rio Grande do Sul', 'São Paulo', 'Vermont', 'Idaho', 'New York']
        self.real_cities = ['ST-FLAVIEN', 'Orizona', 'SAINT-ROCH-DES-AULNAIES', 'Inhuma', 'ST-WENCESLAS', 'Rio Grande', 'Campinas', 'UXBRIDGE', 'NORMANDIN', 'SAINTE-AGNES-DE DUNDEE', 'EAST HARDWICK', 'BURLEY', 'FULTONVILLE']
        
        self.milking_systems = ['Parlor', 'Robot', 'Tie Stall', 'Free Stall']
        self.housing_systems = ['Free Stall', 'Tie Stall', 'Compost Barn', 'Pasture']
        self.feeding_systems = ['TMR', 'Component Feeding', 'Pasture', 'Mixed']
        
        self.visit_names = [
            'Weekly Health Check', 'Nutrition Assessment', 'Herd Evaluation', 'Health Inspection',
            'Production Review', 'Feed Analysis', 'Reproductive Check', 'Facility Audit',
            'Quality Assessment', 'Welfare Inspection', 'Performance Review', 'System Evaluation',
            'Monthly Visit', 'Quarterly Review', 'Annual Assessment', 'Emergency Visit'
        ]
        
        self.animal_classes = [
            'lactatingFresh', 'lactatingMilking', 'dryFarOff', 'dryCloseUp',
            'heifer', 'calf', 'bull', 'replacement'
        ]

    def generate_basic_count_queries(self) -> List[Dict]:
        """Generate basic counting queries for ALL tables"""
        queries = []

        # Count queries for every table
        table_count_queries = [
            # Customer table
            ("How many customers do we have?", "SELECT COUNT(*) FROM customer;"),
            ("What's the total number of accounts?", "SELECT COUNT(*) FROM customer;"),
            ("How many dairy businesses are registered?", "SELECT COUNT(*) FROM customer;"),
            ("Count all customer accounts", "SELECT COUNT(*) FROM customer;"),

            # Site table
            ("How many sites do I have?", "SELECT COUNT(*) FROM site;"),
            ("How many dairy farms are in the database?", "SELECT COUNT(*) FROM site;"),
            ("What's the total number of sites?", "SELECT COUNT(*) FROM site;"),
            ("Count all farm locations", "SELECT COUNT(*) FROM site;"),

            # Visit table
            ("How many visits have been conducted?", "SELECT COUNT(*) FROM visit;"),
            ("What's the total number of farm visits?", "SELECT COUNT(*) FROM visit;"),
            ("How many site inspections are recorded?", "SELECT COUNT(*) FROM visit;"),
            ("Count all farm assessments", "SELECT COUNT(*) FROM visit;"),

            # Pen table
            ("How many pens are there in total?", "SELECT COUNT(*) FROM pen;"),
            ("What's the total number of animal pens?", "SELECT COUNT(*) FROM pen;"),
            ("Count all housing pens", "SELECT COUNT(*) FROM pen;"),
            ("How many animal enclosures exist?", "SELECT COUNT(*) FROM pen;"),

            # Diet table
            ("How many diets are configured?", "SELECT COUNT(*) FROM diets;"),
            ("What's the total number of feeding programs?", "SELECT COUNT(*) FROM diets;"),
            ("Count all nutrition plans", "SELECT COUNT(*) FROM diets;"),
            ("How many feed formulations exist?", "SELECT COUNT(*) FROM diets;"),

            # Notes table
            ("How many notes have been recorded?", "SELECT COUNT(*) FROM noteBook;"),
            ("What's the total number of observations?", "SELECT COUNT(*) FROM noteBook;"),
            ("Count all farm notes", "SELECT COUNT(*) FROM noteBook;"),
            ("How many documentation entries exist?", "SELECT COUNT(*) FROM noteBook;"),

            # Animal class table
            ("How many animal classifications are there?", "SELECT COUNT(*) FROM animalClass;"),
            ("What's the total number of animal categories?", "SELECT COUNT(*) FROM animalClass;"),
            ("Count all animal types", "SELECT COUNT(*) FROM animalClass;"),

            # Contact table
            ("How many contacts are in the system?", "SELECT COUNT(*) FROM contact;"),
            ("What's the total number of contact records?", "SELECT COUNT(*) FROM contact;"),
            ("Count all contact entries", "SELECT COUNT(*) FROM contact;"),

            # Media table
            ("How many media files are stored?", "SELECT COUNT(*) FROM media;"),
            ("What's the total number of uploaded files?", "SELECT COUNT(*) FROM media;"),
            ("Count all media items", "SELECT COUNT(*) FROM media;"),

            # Notifications table
            ("How many notifications exist?", "SELECT COUNT(*) FROM notifications;"),
            ("What's the total number of alerts?", "SELECT COUNT(*) FROM notifications;"),
            ("Count all system notifications", "SELECT COUNT(*) FROM notifications;"),

            # Ear tag table
            ("How many ear tags are registered?", "SELECT COUNT(*) FROM earTag;"),
            ("What's the total number of animal tags?", "SELECT COUNT(*) FROM earTag;"),
            ("Count all ear tag records", "SELECT COUNT(*) FROM earTag;"),

            # Silage table
            ("How many silage records exist?", "SELECT COUNT(*) FROM silage;"),
            ("What's the total number of silage entries?", "SELECT COUNT(*) FROM silage;"),
            ("Count all silage storage records", "SELECT COUNT(*) FROM silage;"),

            # Users table
            ("How many users are in the system?", "SELECT COUNT(*) FROM users;"),
            ("What's the total number of user accounts?", "SELECT COUNT(*) FROM users;"),
            ("Count all system users", "SELECT COUNT(*) FROM users;"),

            # Prospect table
            ("How many prospects are there?", "SELECT COUNT(*) FROM prospect;"),
            ("What's the total number of potential customers?", "SELECT COUNT(*) FROM prospect;"),
            ("Count all prospect records", "SELECT COUNT(*) FROM prospect;"),

            # Visit report table
            ("How many visit reports exist?", "SELECT COUNT(*) FROM visitReport;"),
            ("What's the total number of generated reports?", "SELECT COUNT(*) FROM visitReport;"),
            ("Count all visit report files", "SELECT COUNT(*) FROM visitReport;"),

            # Geographic tables
            ("How many countries are in the database?", "SELECT COUNT(*) FROM countries;"),
            ("How many states are recorded?", "SELECT COUNT(*) FROM states;"),
            ("How many cities are in the system?", "SELECT COUNT(*) FROM cities;"),
            ("What's the total number of segments?", "SELECT COUNT(*) FROM segment;"),
        ]

        for question, sql in table_count_queries:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "basic_count"
            })

        return queries

    def generate_customer_specific_queries(self) -> List[Dict]:
        """Generate comprehensive customer-specific queries using real customer names"""
        queries = []

        # Customer site count queries using real customer names
        for customer in self.real_customers:
            customer_queries = [
                (f"How many sites does customer '{customer}' have?",
                 f"SELECT siteCount FROM customer WHERE businessName = '{customer}';"),
                (f"What is the site count for account '{customer}'?",
                 f"SELECT siteCount FROM customer WHERE businessName = '{customer}';"),
                (f"How many farm locations does '{customer}' operate?",
                 f"SELECT siteCount FROM customer WHERE businessName = '{customer}';"),
                (f"Show me the number of facilities for '{customer}'",
                 f"SELECT siteCount FROM customer WHERE businessName = '{customer}';"),
                (f"Give me name of all the sites customer '{customer}' has",
                 f"SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{customer}';"),
                (f"List all site names for '{customer}'",
                 f"SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{customer}';"),
                (f"What sites does '{customer}' own?",
                 f"SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{customer}';"),
                (f"Show me all farm locations for '{customer}'",
                 f"SELECT s.siteName FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{customer}';"),
            ]
            queries.extend([{
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "customer_specific"
            } for question, sql in customer_queries])

        # Customer information queries
        for customer in self.real_customers[:8]:  # Use first 8 to keep reasonable size
            info_queries = [
                (f"What is the address of '{customer}'?",
                 f"SELECT street, city, state, postalCode FROM customer WHERE businessName = '{customer}';"),
                (f"Where is '{customer}' located?",
                 f"SELECT city, state, country FROM customer WHERE businessName = '{customer}';"),
                (f"What's the customer code for '{customer}'?",
                 f"SELECT customerCode FROM customer WHERE businessName = '{customer}';"),
                (f"Show me the contact information for '{customer}'",
                 f"SELECT contacts FROM customer WHERE businessName = '{customer}';"),
                (f"What is the account type of '{customer}'?",
                 f"SELECT accountType FROM customer WHERE businessName = '{customer}';"),
                (f"When was '{customer}' last visited?",
                 f"SELECT dateOfLastVisit FROM customer WHERE businessName = '{customer}';"),
                (f"Is '{customer}' marked as favourite?",
                 f"SELECT favourite FROM customer WHERE businessName = '{customer}';"),
                (f"Show me all details for customer '{customer}'",
                 f"SELECT * FROM customer WHERE businessName = '{customer}';"),
            ]
            queries.extend([{
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "customer_specific"
            } for question, sql in info_queries])

        # Geographic customer queries
        geographic_queries = [
            ("Show me all customers in Canada",
             "SELECT businessName, city, state FROM customer WHERE country = 'Canada';"),
            ("List all customers in Brazil",
             "SELECT businessName, city, state FROM customer WHERE country = 'Brazil';"),
            ("Show customers in the United States",
             "SELECT businessName, city, state FROM customer WHERE country = 'United States';"),
            ("Find customers with more than 5 sites",
             "SELECT businessName, siteCount FROM customer WHERE siteCount > 5;"),
            ("Show customers with no sites",
             "SELECT businessName FROM customer WHERE siteCount = 0;"),
            ("List all favourite customers",
             "SELECT businessName FROM customer WHERE favourite = 1;"),
            ("Show customers in Quebec",
             "SELECT businessName, city FROM customer WHERE state = 'Quebec';"),
            ("List customers in Ontario",
             "SELECT businessName, city FROM customer WHERE state = 'Ontario';"),
            ("Find customers in São Paulo",
             "SELECT businessName, city FROM customer WHERE state = 'São Paulo';"),
            ("Show customers with account type 1",
             "SELECT businessName FROM customer WHERE accountType = 1;"),
        ]

        for question, sql in geographic_queries:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "customer_specific"
            })

        return queries

    def generate_site_production_queries(self) -> List[Dict]:
        """Generate comprehensive site and production queries"""
        queries = []

        # Basic production metrics
        production_queries = [
            ("What is the average milk production across all sites?", "SELECT AVG(milk) FROM site;"),
            ("What's the total milk production from all sites?", "SELECT SUM(milk) FROM site;"),
            ("What's the maximum milk production recorded?", "SELECT MAX(milk) FROM site;"),
            ("What's the minimum milk production recorded?", "SELECT MIN(milk) FROM site;"),
            ("Show the milk production range", "SELECT MIN(milk) as min_production, MAX(milk) as max_production FROM site;"),
            ("What is the average current milk price?", "SELECT AVG(currentMilkPrice) FROM site;"),
            ("Show sites with milk price above 150", "SELECT siteName, currentMilkPrice FROM site WHERE currentMilkPrice > 150;"),
            ("Find sites with milk price below 50", "SELECT siteName, currentMilkPrice FROM site WHERE currentMilkPrice < 50;"),
        ]

        # Milk quality metrics
        quality_queries = [
            ("Which sites have milk fat percentage above 3.5%?", "SELECT siteName, milkFatPercent FROM site WHERE milkFatPercent > 3.5;"),
            ("Show sites with milk protein above 3.2%", "SELECT siteName, milkProteinPercent FROM site WHERE milkProteinPercent > 3.2;"),
            ("What's the average milk fat percentage?", "SELECT AVG(milkFatPercent) FROM site;"),
            ("What's the average milk protein percentage?", "SELECT AVG(milkProteinPercent) FROM site;"),
            ("Find sites with high somatic cell count", "SELECT siteName, milkSomaticCellCount FROM site WHERE milkSomaticCellCount > 200000;"),
            ("Show sites with low bacteria cell count", "SELECT siteName, bacteriaCellCount FROM site WHERE bacteriaCellCount < 10000;"),
            ("List sites with milk other solids above 5.7%", "SELECT siteName, milkOtherSolidsPercent FROM site WHERE milkOtherSolidsPercent > 5.7;"),
            ("Show sites with excellent milk quality", "SELECT siteName, milkFatPercent, milkProteinPercent FROM site WHERE milkFatPercent > 3.5 AND milkProteinPercent > 3.2;"),
        ]

        # Animal metrics
        animal_queries = [
            ("What's the total number of lactating animals across all farms?", "SELECT SUM(lactatingAnimal) FROM site;"),
            ("What's the average number of lactating animals per site?", "SELECT AVG(lactatingAnimal) FROM site;"),
            ("Which sites have more than 500 lactating animals?", "SELECT siteName, lactatingAnimal FROM site WHERE lactatingAnimal > 500;"),
            ("What's the average days in milk for all sites?", "SELECT AVG(daysInMilk) FROM site;"),
            ("Show sites with days in milk above 200", "SELECT siteName, daysInMilk FROM site WHERE daysInMilk > 200;"),
            ("Find sites with less than 100 lactating animals", "SELECT siteName, lactatingAnimal FROM site WHERE lactatingAnimal < 100;"),
        ]

        # Facility metrics
        facility_queries = [
            ("Show me sites with more than 100 parlor stalls", "SELECT siteName, numberOfParlorStalls FROM site WHERE numberOfParlorStalls > 100;"),
            ("What's the average number of parlor stalls?", "SELECT AVG(numberOfParlorStalls) FROM site;"),
            ("Which sites have the most pens?", "SELECT siteName, penCount FROM site ORDER BY penCount DESC LIMIT 10;"),
            ("Show sites with more than 20 pens", "SELECT siteName, penCount FROM site WHERE penCount > 20;"),
            ("List sites by milking system type", "SELECT milkingSystemType, COUNT(*) as site_count FROM site GROUP BY milkingSystemType;"),
            ("Show all parlor milking sites", "SELECT siteName FROM site WHERE milkingSystemType = 'Parlor';"),
            ("Find all robot milking sites", "SELECT siteName FROM site WHERE milkingSystemType = 'Robot';"),
            ("Show sites with tie stall systems", "SELECT siteName FROM site WHERE milkingSystemType = 'Tie Stall';"),
        ]

        # Feed and nutrition metrics
        nutrition_queries = [
            ("What's the average dry matter intake across sites?", "SELECT AVG(dryMatterIntake) FROM site;"),
            ("Show sites with high as-fed intake", "SELECT siteName, asFedIntake FROM site WHERE asFedIntake > 50;"),
            ("What's the average ration cost per site?", "SELECT AVG(rationCost) FROM site;"),
            ("Find sites with ration cost above 5", "SELECT siteName, rationCost FROM site WHERE rationCost > 5.0;"),
            ("Show the net energy levels across sites", "SELECT siteName, netEnergyOfLactationDairy FROM site WHERE netEnergyOfLactationDairy IS NOT NULL;"),
            ("Find sites with low dry matter intake", "SELECT siteName, dryMatterIntake FROM site WHERE dryMatterIntake < 20;"),
        ]

        all_queries = production_queries + quality_queries + animal_queries + facility_queries + nutrition_queries

        for question, sql in all_queries:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "site_production"
            })

        return queries

    def generate_visit_queries(self) -> List[Dict]:
        """Generate comprehensive visit queries"""
        queries = []

        # Specific visit queries using real visit names
        for visit_name in self.visit_names:
            visit_queries = [
                (f"What data is inside visit '{visit_name}'?",
                 f"SELECT * FROM visit WHERE visitName = '{visit_name}';"),
                (f"Show me all information for visit '{visit_name}'",
                 f"SELECT * FROM visit WHERE visitName = '{visit_name}';"),
                (f"Who conducted the visit named '{visit_name}'?",
                 f"SELECT firstName, lastName FROM visit WHERE visitName = '{visit_name}';"),
                (f"What was the rumen health score for visit '{visit_name}'?",
                 f"SELECT rumenHealth FROM visit WHERE visitName = '{visit_name}';"),
                (f"Show the locomotion score for '{visit_name}'",
                 f"SELECT locomotionScore FROM visit WHERE visitName = '{visit_name}';"),
                (f"What was the body condition score in '{visit_name}'?",
                 f"SELECT bodyCondition FROM visit WHERE visitName = '{visit_name}';"),
                (f"When was visit '{visit_name}' conducted?",
                 f"SELECT visitDate FROM visit WHERE visitName = '{visit_name}';"),
                (f"What is the status of visit '{visit_name}'?",
                 f"SELECT visitStatus FROM visit WHERE visitName = '{visit_name}';"),
            ]

            # Add only first 6 visit names to keep dataset manageable
            if self.visit_names.index(visit_name) < 6:
                queries.extend([{
                    "instruction": "Generate SQL query for the following question about dairy farm data:",
                    "input": question,
                    "output": sql,
                    "category": "visit_data"
                } for question, sql in visit_queries])

        # General visit queries
        general_visit_queries = [
            ("Show all visits from the last month",
             "SELECT visitName, visitDate, firstName, lastName FROM visit WHERE visitDate >= date('now', '-1 month');"),
            ("List all completed visits",
             "SELECT visitName, visitDate FROM visit WHERE visitStatus = 'completed';"),
            ("Find visits with high rumen health scores",
             "SELECT visitName, rumenHealth FROM visit WHERE rumenHealth > 3;"),
            ("Show visits with poor locomotion scores",
             "SELECT visitName, locomotionScore FROM visit WHERE locomotionScore < 2;"),
            ("List all visits by John Smith",
             "SELECT visitName, visitDate FROM visit WHERE firstName = 'John' AND lastName = 'Smith';"),
            ("Find visits with heat stress issues",
             "SELECT visitName, heatStress FROM visit WHERE heatStress IS NOT NULL;"),
            ("Show all published visits",
             "SELECT visitName, visitPublishedDate FROM visit WHERE isVisitAutoPublished = 1;"),
            ("Find visits with profitability analysis",
             "SELECT visitName, profitabilityAnalysis FROM visit WHERE profitabilityAnalysis IS NOT NULL;"),
            ("Show visits with calf heifer scorecard",
             "SELECT visitName, calfHeiferScorecard FROM visit WHERE calfHeiferScorecard IS NOT NULL;"),
            ("List visits with robotic milk evaluation",
             "SELECT visitName, roboticMilkEvaluation FROM visit WHERE roboticMilkEvaluation IS NOT NULL;"),
            ("Show visits with TMR particle scores",
             "SELECT visitName, tmrParticleScore FROM visit WHERE tmrParticleScore IS NOT NULL;"),
            ("Find visits with manure screening results",
             "SELECT visitName, manureScreener FROM visit WHERE manureScreener IS NOT NULL;"),
            ("Show visits with forage audit scorecards",
             "SELECT visitName, forageAuditScorecard FROM visit WHERE forageAuditScorecard IS NOT NULL;"),
            ("List visits with metabolic incidence data",
             "SELECT visitName, metabolicIncidence FROM visit WHERE metabolicIncidence IS NOT NULL;"),
        ]

        for question, sql in general_visit_queries:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "visit_data"
            })

        return queries

    def generate_complex_join_queries(self) -> List[Dict]:
        """Generate comprehensive join queries across all tables"""
        queries = []

        # Customer-Site joins using real customer names
        for customer in self.real_customers[:6]:  # Use first 6 customers
            customer_site_queries = [
                (f"Show me all sites for customer '{customer}'",
                 f"SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{customer}';"),
                (f"What are the production metrics for '{customer}' sites?",
                 f"SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{customer}';"),
                (f"Show facility details for '{customer}'",
                 f"SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{customer}';"),
                (f"What visits were conducted at sites owned by '{customer}'?",
                 f"SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{customer}';"),
                (f"Show visit assessments for '{customer}' farms",
                 f"SELECT v.visitName, v.rumenHealth, v.locomotionScore, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{customer}';"),
            ]

            queries.extend([{
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "complex_joins"
            } for question, sql in customer_site_queries])

        # Pen aggregation queries
        pen_queries = [
            ("How many pens does each customer have in total?",
             "SELECT c.businessName, COUNT(p.id) as total_pens FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;"),
            ("What's the total number of animals per customer?",
             "SELECT c.businessName, SUM(p.animals) as total_animals FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;"),
            ("Show average milk production per pen by customer",
             "SELECT c.businessName, AVG(p.milk) as avg_pen_milk FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;"),
            ("Count pens by housing system type",
             "SELECT housingSystemType, COUNT(*) as pen_count FROM pen GROUP BY housingSystemType;"),
            ("Show feeding system distribution",
             "SELECT feedingSystemType, COUNT(*) as pen_count FROM pen GROUP BY feedingSystemType;"),
            ("Show pen details with site information",
             "SELECT p.name as pen_name, p.animals, s.siteName, c.businessName FROM pen p JOIN site s ON p.siteId = s.id JOIN customer c ON s.accountId = c.id;"),
        ]

        # Diet-related joins
        diet_queries = [
            ("Show diets used at each site",
             "SELECT s.siteName, d.name as diet_name, d.numberOfAnimals FROM site s JOIN diets d ON s.id = d.siteId;"),
            ("Which customers use the most diet programs?",
             "SELECT c.businessName, COUNT(d.id) as diet_count FROM customer c JOIN site s ON c.id = s.accountId JOIN diets d ON s.id = d.siteId GROUP BY c.businessName ORDER BY diet_count DESC;"),
            ("Show active diets by animal type",
             "SELECT animalType, COUNT(*) as diet_count FROM diets WHERE isActive = 1 GROUP BY animalType;"),
            ("Show diet details with customer information",
             "SELECT d.name as diet_name, d.animalType, d.numberOfAnimals, c.businessName FROM diets d JOIN site s ON d.siteId = s.id JOIN customer c ON s.accountId = c.id;"),
        ]

        # Note-related joins
        note_queries = [
            ("Show notes for each customer",
             "SELECT c.businessName, n.title, n.note FROM customer c JOIN noteBook n ON c.id = n.accountId;"),
            ("Count notes per site",
             "SELECT s.siteName, COUNT(n.id) as note_count FROM site s LEFT JOIN noteBook n ON s.id = n.siteId GROUP BY s.siteName;"),
            ("Show favourite notes by customer",
             "SELECT c.businessName, n.title FROM customer c JOIN noteBook n ON c.id = n.accountId WHERE n.favourite = 1;"),
            ("Show notes with visit information",
             "SELECT n.title, n.note, v.visitName, v.visitDate FROM noteBook n JOIN visit v ON n.visitId = v.id;"),
        ]

        all_join_queries = pen_queries + diet_queries + note_queries

        for question, sql in all_join_queries:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "complex_joins"
            })

        return queries

    def generate_all_tables_queries(self) -> List[Dict]:
        """Generate queries for ALL remaining tables"""
        queries = []

        # Animal class queries
        animal_class_queries = [
            ("Show all animal classifications", "SELECT className, en_class, en_subClass FROM animalClass;"),
            ("List lactating animal categories", "SELECT className, en_class FROM animalClass WHERE en_class LIKE '%lactating%';"),
            ("Show dry cow classifications", "SELECT className, en_class FROM animalClass WHERE en_class LIKE '%dry%';"),
            ("Find heifer classifications", "SELECT className, en_class FROM animalClass WHERE en_class LIKE '%heifer%';"),
            ("Show all animal types in English", "SELECT en_class, en_subClass FROM animalClass;"),
            ("List animal classes in French", "SELECT fr_class, fr_subClass FROM animalClass;"),
            ("Show German animal classifications", "SELECT de_class, de_subClass FROM animalClass;"),
        ]

        # Contact queries
        contact_queries = [
            ("Show all contacts", "SELECT fullName, email, phoneNumber FROM contact;"),
            ("Find contacts with email addresses", "SELECT fullName, email FROM contact WHERE email IS NOT NULL;"),
            ("Show contacts with phone numbers", "SELECT fullName, phoneNumber FROM contact WHERE phoneNumber IS NOT NULL;"),
            ("List all contact names", "SELECT fullName FROM contact;"),
        ]

        # Media queries
        media_queries = [
            ("Show all media files", "SELECT filename, mediaName, mediaType FROM media;"),
            ("List image files", "SELECT filename, mediaName FROM media WHERE mediaType = 'image';"),
            ("Show video files", "SELECT filename, mediaName FROM media WHERE mediaType = 'video';"),
            ("Find media files by visit", "SELECT m.filename, v.visitName FROM media m JOIN visit v ON m.visitId = v.id;"),
            ("Show media files with notes", "SELECT m.filename, n.title FROM media m JOIN noteBook n ON m.noteId = n.id;"),
        ]

        # Notification queries
        notification_queries = [
            ("Show all notifications", "SELECT title, description, type FROM notifications;"),
            ("List unread notifications", "SELECT title, description FROM notifications WHERE isRead = 0;"),
            ("Show read notifications", "SELECT title, description FROM notifications WHERE isRead = 1;"),
            ("Find notifications by type", "SELECT title, description FROM notifications WHERE type = 'alert';"),
        ]

        # Ear tag queries
        ear_tag_queries = [
            ("Show all ear tags", "SELECT earTagName FROM earTag;"),
            ("List ear tags by customer", "SELECT e.earTagName, c.businessName FROM earTag e JOIN customer c ON e.accountId = c.id;"),
            ("Show ear tags by site", "SELECT e.earTagName, s.siteName FROM earTag e JOIN site s ON e.siteId = s.id;"),
        ]

        # Silage queries
        silage_queries = [
            ("Show all silage records", "SELECT silageName FROM silage;"),
            ("List silage by customer", "SELECT s.silageName, c.businessName FROM silage s JOIN customer c ON s.accountId = c.id;"),
            ("Show silage by site", "SELECT s.silageName, st.siteName FROM silage s JOIN site st ON s.siteId = st.id;"),
        ]

        # User queries
        user_queries = [
            ("Show all users", "SELECT fullName, email FROM users;"),
            ("List logged in users", "SELECT fullName, email FROM users WHERE isLoggedIn = 1;"),
            ("Show users by country", "SELECT fullName, countryId FROM users;"),
            ("Find users by email domain", "SELECT fullName, email FROM users WHERE email LIKE '%@example.com';"),
        ]

        # Prospect queries
        prospect_queries = [
            ("Show all prospects", "SELECT businessName, country FROM prospect;"),
            ("List prospects by country", "SELECT businessName FROM prospect WHERE country = 'Canada';"),
            ("Show favourite prospects", "SELECT businessName FROM prospect WHERE favourite = 1;"),
        ]

        # Visit report queries
        visit_report_queries = [
            ("Show all visit reports", "SELECT fileName FROM visitReport;"),
            ("List reports by visit", "SELECT vr.fileName, v.visitName FROM visitReport vr JOIN visit v ON vr.visitId = v.id;"),
            ("Show synced reports", "SELECT fileName FROM visitReport WHERE is_synced = 1;"),
        ]

        # Geographic queries
        geographic_queries = [
            ("Show all countries", "SELECT name, countryCode FROM countries;"),
            ("List all states", "SELECT name, stateCode FROM states;"),
            ("Show all cities", "SELECT name, countryName FROM cities;"),
            ("Show states by country", "SELECT s.name, c.name as country FROM states s JOIN countries c ON s.countryCode = c.countryCode;"),
        ]

        # Segment queries
        segment_queries = [
            ("Show all segments", "SELECT name, defaultValue FROM segment;"),
            ("List customers by segment", "SELECT c.businessName, s.name as segment FROM customer c JOIN segment s ON c.segmentId = s.id;"),
        ]

        all_table_queries = (animal_class_queries + contact_queries + media_queries +
                           notification_queries + ear_tag_queries + silage_queries +
                           user_queries + prospect_queries + visit_report_queries +
                           geographic_queries + segment_queries)

        for question, sql in all_table_queries:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "all_tables"
            })

        return queries

    def generate_advanced_analytics_queries(self) -> List[Dict]:
        """Generate advanced analytics and reporting queries"""
        queries = []

        analytics_queries = [
            # Performance analysis
            ("Show top 10 performing sites by milk production",
             "SELECT siteName, milk FROM site ORDER BY milk DESC LIMIT 10;"),
            ("Find sites with declining performance",
             "SELECT siteName, milk, milkFatPercent FROM site WHERE milk < (SELECT AVG(milk) FROM site);"),
            ("Show correlation between parlor stalls and production",
             "SELECT numberOfParlorStalls, AVG(milk) as avg_production FROM site GROUP BY numberOfParlorStalls;"),
            ("Identify high-maintenance sites",
             "SELECT s.siteName, COUNT(v.id) as visit_count FROM site s LEFT JOIN visit v ON s.id = v.siteId GROUP BY s.siteName HAVING visit_count > 5;"),

            # Geographic analysis
            ("Show customer distribution by country",
             "SELECT country, COUNT(*) as customer_count FROM customer GROUP BY country;"),
            ("List sites by state with production totals",
             "SELECT c.state, COUNT(s.id) as site_count, SUM(s.milk) as total_production FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.state;"),
            ("Show average milk production by country",
             "SELECT c.country, AVG(s.milk) as avg_production FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.country;"),

            # Time-based analysis
            ("Show recent visits with site information",
             "SELECT v.visitName, v.visitDate, s.siteName, c.businessName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE v.visitDate >= date('now', '-30 days');"),
            ("Count visits per month by customer",
             "SELECT c.businessName, strftime('%Y-%m', v.visitDate) as month, COUNT(v.id) as visit_count FROM customer c JOIN site s ON c.id = s.accountId JOIN visit v ON s.id = v.siteId GROUP BY c.businessName, month;"),

            # Efficiency metrics
            ("Show efficiency metrics by region",
             "SELECT c.state, AVG(s.milk/s.lactatingAnimal) as efficiency FROM customer c JOIN site s ON c.id = s.accountId WHERE s.lactatingAnimal > 0 GROUP BY c.state;"),
            ("Identify underperforming pens",
             "SELECT name, milk/animals as efficiency FROM pen WHERE animals > 0 ORDER BY efficiency ASC LIMIT 10;"),
            ("Find customers with multiple site types",
             "SELECT c.businessName, COUNT(DISTINCT s.milkingSystemType) as system_types FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.businessName HAVING system_types > 1;"),

            # Quality analysis
            ("Show sites with best milk quality scores",
             "SELECT siteName, milkFatPercent, milkProteinPercent, milkSomaticCellCount FROM site WHERE milkFatPercent > 3.5 AND milkProteinPercent > 3.2 AND milkSomaticCellCount < 200000;"),
            ("Find sites needing attention",
             "SELECT siteName, milkSomaticCellCount, bacteriaCellCount FROM site WHERE milkSomaticCellCount > 400000 OR bacteriaCellCount > 20000;"),
        ]

        for question, sql in analytics_queries:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "advanced_analytics"
            })

        return queries

    def generate_full_dataset(self) -> List[Dict]:
        """Generate the complete comprehensive training dataset"""
        dataset = []

        print("🐄 Generating COMPREHENSIVE dairy database training dataset...")
        print("📊 Including ALL tables and relationships from your schema")
        print("=" * 60)

        # Generate all types of queries
        dataset.extend(self.generate_basic_count_queries())
        print(f"✅ Basic count queries: {len([q for q in dataset if q['category'] == 'basic_count'])}")

        dataset.extend(self.generate_customer_specific_queries())
        print(f"✅ Customer-specific queries: {len([q for q in dataset if q['category'] == 'customer_specific'])}")

        dataset.extend(self.generate_site_production_queries())
        print(f"✅ Site/production queries: {len([q for q in dataset if q['category'] == 'site_production'])}")

        dataset.extend(self.generate_visit_queries())
        print(f"✅ Visit queries: {len([q for q in dataset if q['category'] == 'visit_data'])}")

        dataset.extend(self.generate_complex_join_queries())
        print(f"✅ Complex join queries: {len([q for q in dataset if q['category'] == 'complex_joins'])}")

        dataset.extend(self.generate_all_tables_queries())
        print(f"✅ All tables queries: {len([q for q in dataset if q['category'] == 'all_tables'])}")

        dataset.extend(self.generate_advanced_analytics_queries())
        print(f"✅ Advanced analytics queries: {len([q for q in dataset if q['category'] == 'advanced_analytics'])}")

        # Shuffle the dataset
        random.shuffle(dataset)

        print("=" * 60)
        print(f"🎯 TOTAL DATASET SIZE: {len(dataset)} samples")
        print("📋 Coverage includes:")
        print("   • ALL 17 database tables")
        print("   • Real customer names from your data")
        print("   • Complex multi-table joins")
        print("   • Production metrics and analytics")
        print("   • Geographic and time-based queries")
        print("   • Advanced reporting queries")

        return dataset

    def save_dataset(self, filename: str = "comprehensive_dairy_sql_dataset.json"):
        """Save the comprehensive dataset to a JSON file"""
        dataset = self.generate_full_dataset()

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Dataset saved to: {filename}")

        # Show sample entries from different categories
        categories = {}
        for sample in dataset:
            cat = sample['category']
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(sample)

        print(f"\n📋 Sample entries by category:")
        for category, samples in categories.items():
            print(f"\n🔸 {category.upper()}:")
            sample = samples[0]
            print(f"   Q: {sample['input']}")
            print(f"   SQL: {sample['output']}")

if __name__ == "__main__":
    generator = ComprehensiveDairyDatasetGenerator()
    generator.save_dataset()
