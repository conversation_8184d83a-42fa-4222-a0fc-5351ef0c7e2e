[{"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Nutrition Assessment'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Welfare Inspection'", "output": "SELECT * FROM visit WHERE visitName = 'Welfare Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Herd Evaluation'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Prairie View Dairy'", "output": "SELECT contacts FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Heritage Dairy'", "output": "SELECT contacts FROM customer WHERE businessName = 'Heritage Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show facility details for 'Green Valley Dairy'", "output": "SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Green Valley Dairy';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Maple Ridge Dairy'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Maple Ridge Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the milk production range", "output": "SELECT MIN(milk) as min_production, MAX(milk) as max_production FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Welfare Inspection' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Welfare Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total milk production from all sites?", "output": "SELECT SUM(milk) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Nutrition Assessment' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all housing pens", "output": "SELECT COUNT(*) FROM pen;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with days in milk above 200", "output": "SELECT siteName, daysInMilk FROM site WHERE daysInMilk > 200;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Facility Audit' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Facility Audit';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show average milk production by country", "output": "SELECT c.country, AVG(s.milk) as avg_production FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.country;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Herd Evaluation'", "output": "SELECT * FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Reproductive Check'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Reproductive Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Welfare Inspection'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Welfare Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations do we have?", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many visits have been conducted?", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Health Inspection'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with high as-fed intake", "output": "SELECT siteName, asFedIntake FROM site WHERE asFedIntake > 50;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Cloverfield Farm'", "output": "SELECT contacts FROM customer WHERE businessName = 'Cloverfield Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Valley View Farm'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Valley View Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visit assessments for 'Prairie View Dairy' farms", "output": "SELECT v.visitName, v.rumenHealth, v.locomotionScore, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Prairie View Dairy';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many animal classifications are there?", "output": "SELECT COUNT(*) FROM animalClass;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'System Evaluation' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'System Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Sunrise Dairy Farm' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show pens with high dry matter intake", "output": "SELECT name, dryMatterIntake FROM pen WHERE dryMatterIntake > 25;", "category": "pen_queries"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Performance Review'?", "output": "SELECT * FROM visit WHERE visitName = 'Performance Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Reproductive Check'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Reproductive Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Herd Evaluation'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the net energy levels across sites", "output": "SELECT siteName, netEnergyOfLactationDairy FROM site WHERE netEnergyOfLactationDairy IS NOT NULL;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Facility Audit'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Facility Audit';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Identify high-maintenance sites", "output": "SELECT s.siteName, COUNT(v.id) as visit_count FROM site s LEFT JOIN visit v ON s.id = v.siteId GROUP BY s.siteName HAVING visit_count > 5;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Reproductive Check'", "output": "SELECT * FROM visit WHERE visitName = 'Reproductive Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Welfare Inspection'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Welfare Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Reproductive Check' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Reproductive Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Hillside Dairy Co' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Valley View Farm' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Valley View Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Heritage Dairy' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Heritage Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Valley View Farm'", "output": "SELECT * FROM customer WHERE businessName = 'Valley View Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Total customer accounts", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Valley View Farm' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Valley View Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find customers with multiple site types", "output": "SELECT c.businessName, COUNT(DISTINCT s.milkingSystemType) as system_types FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.businessName HAVING system_types > 1;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Hillside Dairy Co'", "output": "SELECT contacts FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Sunrise Dairy Farm'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'System Evaluation'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'System Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What visits were conducted at sites owned by 'Prairie View Dairy'?", "output": "SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Prairie View Dairy';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Green Valley Dairy'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find heifer classifications", "output": "SELECT className, en_class FROM animalClass WHERE en_class = 'Heifer';", "category": "animal_class"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show efficiency metrics by region", "output": "SELECT c.state, AVG(s.milk/s.lactatingAnimal) as efficiency FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.state;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Cloverfield Farm' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Cloverfield Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Health Inspection'", "output": "SELECT * FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Welfare Inspection'?", "output": "SELECT * FROM visit WHERE visitName = 'Welfare Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average milk fat percentage?", "output": "SELECT AVG(milkFatPercent) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Hillside Dairy Co'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Which sites have the most pens?", "output": "SELECT siteName, penCount FROM site ORDER BY penCount DESC LIMIT 10;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Feed Analysis'", "output": "SELECT * FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Green Valley Dairy'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show correlation between parlor stalls and production", "output": "SELECT numberOfParlorStalls, AVG(milk) as avg_production FROM site GROUP BY numberOfParlorStalls;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Quality Assessment'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Quality Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Green Valley Dairy'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visit assessments for 'Meadowbrook Farm' farms", "output": "SELECT v.visitName, v.rumenHealth, v.locomotionScore, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Meadowbrook Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Hillside Dairy Co'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visit assessments for 'Sunrise Dairy Farm' farms", "output": "SELECT v.visitName, v.rumenHealth, v.locomotionScore, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Sunrise Dairy Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Weekly Health Check' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Valley View Farm'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Valley View Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find pens with specific feeding systems", "output": "SELECT name, feedingSystemType FROM pen WHERE feedingSystemType = 'TMR';", "category": "pen_queries"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Weekly Health Check'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Mountain View Ranch'", "output": "SELECT contacts FROM customer WHERE businessName = 'Mountain View Ranch';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Maple Ridge Dairy'", "output": "SELECT contacts FROM customer WHERE businessName = 'Maple Ridge Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average number of animals per pen?", "output": "SELECT AVG(animals) FROM pen;", "category": "pen_queries"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Meadowbrook Farm' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Facility Audit'", "output": "SELECT * FROM visit WHERE visitName = 'Facility Audit';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all visits from the last month", "output": "SELECT visitName, visitDate, firstName, lastName FROM visit WHERE visitDate >= date('now', '-1 month');", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Maple Ridge Dairy' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Maple Ridge Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Cloverfield Farm' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Cloverfield Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Production Review'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show customers with no sites", "output": "SELECT businessName FROM customer WHERE siteCount = 0;", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Herd Evaluation'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Facility Audit'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Facility Audit';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'Prairie View Dairy'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Prairie View Dairy';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Quality Assessment'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Quality Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Golden Fields Farm' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Weekly Health Check'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show feeding system distribution", "output": "SELECT feedingSystemType, COUNT(*) as pen_count FROM pen GROUP BY feedingSystemType;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Valley View Farm' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Valley View Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Cloverfield Farm'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Cloverfield Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'Hillside Dairy Co'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Hillside Dairy Co';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'Green Valley Dairy'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Green Valley Dairy';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Valley View Farm'", "output": "SELECT contacts FROM customer WHERE businessName = 'Valley View Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Maple Ridge Dairy' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Maple Ridge Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List visits that need sync", "output": "SELECT visitName FROM visit WHERE needsSync = 1;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show recent visits with site information", "output": "SELECT v.visitName, v.visitDate, s.siteName, c.businessName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE v.visitDate >= date('now', '-30 days');", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all farm visits", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Reproductive Check'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Reproductive Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Facility Audit'?", "output": "SELECT * FROM visit WHERE visitName = 'Facility Audit';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Golden Fields Farm' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Nutrition Assessment'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Hillside Dairy Co' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Mountain View Ranch' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Mountain View Ranch';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Green Valley Dairy' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Heritage Dairy'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Heritage Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find pens with low ration cost", "output": "SELECT name, rationCostPerAnimal FROM pen WHERE rationCostPerAnimal < 3.0;", "category": "pen_queries"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What are the production metrics for 'Sunrise Dairy Farm' sites?", "output": "SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Sunrise Dairy Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List sites with milk other solids above 5.7%", "output": "SELECT siteName, milkOtherSolidsPercent FROM site WHERE milkOtherSolidsPercent > 5.7;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show facility details for 'Golden Fields Farm'", "output": "SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Golden Fields Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all farm notes", "output": "SELECT COUNT(*) FROM noteBook;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Green Valley Dairy' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List lactating animal categories", "output": "SELECT className, en_class FROM animalClass WHERE en_class = 'Lactating';", "category": "animal_class"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the current milk price range?", "output": "SELECT MIN(currentMilkPrice) as min_price, MAX(currentMilkPrice) as max_price FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show favourite notes by customer", "output": "SELECT c.businessName, n.title FROM customer c JOIN noteBook n ON c.id = n.accountId WHERE n.favourite = 1;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of animal categories?", "output": "SELECT COUNT(*) FROM animalClass;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visits with calf heifer scorecard", "output": "SELECT visitName, calfHeiferScorecard FROM visit WHERE calfHeiferScorecard IS NOT NULL;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of lactating animals across all farms?", "output": "SELECT SUM(lactatingAnimal) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What visits were conducted at sites owned by 'Sunrise Dairy Farm'?", "output": "SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Sunrise Dairy Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Identify underperforming pens", "output": "SELECT name, milk/animals as efficiency FROM pen WHERE animals > 0 ORDER BY efficiency ASC LIMIT 10;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Weekly Health Check'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Mountain View Ranch'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Mountain View Ranch';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What are the production metrics for 'Golden Fields Farm' sites?", "output": "SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Golden Fields Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What visits were conducted at sites owned by 'Meadowbrook Farm'?", "output": "SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Meadowbrook Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Quality Assessment'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Quality Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Prairie View Dairy'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Riverside Dairy'", "output": "SELECT * FROM customer WHERE businessName = 'Riverside Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Production Review'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Green Valley Dairy' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Sunrise Dairy Farm'", "output": "SELECT * FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Riverside Dairy' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Riverside Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List pens with high milking frequency", "output": "SELECT name, milkingFrequency FROM pen WHERE milkingFrequency > 2;", "category": "pen_queries"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Golden Fields Farm'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Prairie View Dairy'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me sites with more than 100 parlor stalls", "output": "SELECT siteName, numberOfParlorStalls FROM site WHERE numberOfParlorStalls > 100;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Feed Analysis'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Health Inspection'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'System Evaluation'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'System Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show facility details for 'Meadowbrook Farm'", "output": "SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Meadowbrook Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find sites with high somatic cell count", "output": "SELECT siteName, milkSomaticCellCount FROM site WHERE milkSomaticCellCount > 200000;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Riverside Dairy' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Riverside Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show top 10 performing sites by milk production", "output": "SELECT siteName, milk FROM site ORDER BY milk DESC LIMIT 10;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Meadowbrook Farm' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show German animal classifications", "output": "SELECT de_class, de_subClass FROM animalClass;", "category": "animal_class"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Quality Assessment'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Quality Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Facility Audit'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Facility Audit';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Riverside Dairy'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Riverside Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Mountain View Ranch' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Mountain View Ranch';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Hillside Dairy Co'", "output": "SELECT * FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Green Valley Dairy'", "output": "SELECT * FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Sunrise Dairy Farm' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Performance Review'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Performance Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Mountain View Ranch' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Mountain View Ranch';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Sunrise Dairy Farm' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of accounts?", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many assessments were performed?", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Performance Review'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Performance Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Prairie View Dairy'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Cloverfield Farm'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Cloverfield Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Mountain View Ranch'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Mountain View Ranch';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Golden Fields Farm'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Welfare Inspection'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Welfare Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What are the production metrics for 'Prairie View Dairy' sites?", "output": "SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Prairie View Dairy';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Production Review' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Health Inspection'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Nutrition Assessment'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Maple Ridge Dairy'", "output": "SELECT * FROM customer WHERE businessName = 'Maple Ridge Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show dry cow classifications", "output": "SELECT className, en_class FROM animalClass WHERE en_class = 'Dry';", "category": "animal_class"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Meadowbrook Farm' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Valley View Farm'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Valley View Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show pen utilization by barn", "output": "SELECT barnName, COUNT(*) as pen_count FROM pen GROUP BY barnName;", "category": "pen_queries"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Golden Fields Farm'", "output": "SELECT contacts FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Cloverfield Farm'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Cloverfield Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Heritage Dairy' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Heritage Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average number of parlor stalls?", "output": "SELECT AVG(numberOfParlorStalls) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List sites by state with production totals", "output": "SELECT c.state, COUNT(s.id) as site_count, SUM(s.milk) as total_production FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.state;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'Meadowbrook Farm'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Meadowbrook Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find visits with profitability analysis", "output": "SELECT visitName, profitabilityAnalysis FROM visit WHERE profitabilityAnalysis IS NOT NULL;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Riverside Dairy' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Riverside Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Quality Assessment'?", "output": "SELECT * FROM visit WHERE visitName = 'Quality Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Sunrise Dairy Farm'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Hillside Dairy Co' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Prairie View Dairy'", "output": "SELECT * FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visit assessments for 'Green Valley Dairy' farms", "output": "SELECT v.visitName, v.rumenHealth, v.locomotionScore, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Green Valley Dairy';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Meadowbrook Farm'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Total number of farm facilities", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Facility Audit'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Facility Audit';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Green Valley Dairy' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many feed formulations exist?", "output": "SELECT COUNT(*) FROM diets;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all customers", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all visits by <PERSON>", "output": "SELECT visitName, visitDate FROM visit WHERE firstName = '<PERSON>' AND lastName = '<PERSON>';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Quality Assessment'", "output": "SELECT * FROM visit WHERE visitName = 'Quality Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average days in milk for all sites?", "output": "SELECT AVG(daysInMilk) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with more than 20 pens", "output": "SELECT siteName, penCount FROM site WHERE penCount > 20;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Mountain View Ranch'", "output": "SELECT * FROM customer WHERE businessName = 'Mountain View Ranch';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Prairie View Dairy' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Meadowbrook Farm' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many dairy farms are in the database?", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Golden Fields Farm'", "output": "SELECT * FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'Sunrise Dairy Farm'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Sunrise Dairy Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Performance Review'", "output": "SELECT * FROM visit WHERE visitName = 'Performance Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Cloverfield Farm' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Cloverfield Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Production Review'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Prairie View Dairy' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many site inspections do we have recorded?", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show seasonal visit patterns", "output": "SELECT strftime('%m', visitDate) as month, COUNT(*) as visit_count FROM visit GROUP BY month;", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show active diets by animal type", "output": "SELECT animalType, COUNT(*) as diet_count FROM diets WHERE isActive = 1 GROUP BY animalType;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many customers do we have?", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find customers with more than 5 sites", "output": "SELECT businessName, siteCount FROM customer WHERE siteCount > 5;", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Herd Evaluation' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many notes have been recorded?", "output": "SELECT COUNT(*) FROM noteBook;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average ration cost per site?", "output": "SELECT AVG(rationCost) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the average milk production across all sites?", "output": "SELECT AVG(milk) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Green Valley Dairy'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Valley View Farm'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Valley View Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Performance Review'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Performance Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all animal types", "output": "SELECT COUNT(*) FROM animalClass;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many pens are there in total?", "output": "SELECT COUNT(*) FROM pen;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Weekly Health Check'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count notes per site", "output": "SELECT s.siteName, COUNT(n.id) as note_count FROM site s LEFT JOIN noteBook n ON s.id = n.siteId GROUP BY s.siteName;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Golden Fields Farm'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many pens does each customer have in total?", "output": "SELECT c.businessName, COUNT(p.id) as total_pens FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all customers in Canada", "output": "SELECT businessName, city, state FROM customer WHERE country = 'Canada';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Riverside Dairy'", "output": "SELECT contacts FROM customer WHERE businessName = 'Riverside Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'System Evaluation'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'System Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all pens with more than 100 animals", "output": "SELECT name, animals FROM pen WHERE animals > 100;", "category": "pen_queries"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What are the production metrics for 'Hillside Dairy Co' sites?", "output": "SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Hillside Dairy Co';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Hillside Dairy Co' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Riverside Dairy'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Riverside Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Production Review'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of animals per customer?", "output": "SELECT c.businessName, SUM(p.animals) as total_animals FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show customers in California", "output": "SELECT businessName, city FROM customer WHERE state = 'California';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count visits per month by customer", "output": "SELECT c.businessName, strftime('%Y-%m', v.visitDate) as month, COUNT(v.id) as visit_count FROM customer c JOIN site s ON c.id = s.accountId JOIN visit v ON s.id = v.siteId GROUP BY c.businessName, month;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Quality Assessment'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Quality Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all nutrition plans", "output": "SELECT COUNT(*) FROM diets;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show customer distribution by country", "output": "SELECT country, COUNT(*) as customer_count FROM customer GROUP BY country;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Mountain View Ranch' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Mountain View Ranch';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Mountain View Ranch'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Mountain View Ranch';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many client accounts exist?", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'System Evaluation'", "output": "SELECT * FROM visit WHERE visitName = 'System Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Heritage Dairy'", "output": "SELECT * FROM customer WHERE businessName = 'Heritage Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Health Inspection'?", "output": "SELECT * FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Reproductive Check'?", "output": "SELECT * FROM visit WHERE visitName = 'Reproductive Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average milk price?", "output": "SELECT AVG(currentMilkPrice) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Feed Analysis'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Prairie View Dairy' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Meadowbrook Farm'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Production Review'", "output": "SELECT * FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Green Valley Dairy'", "output": "SELECT contacts FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Maple Ridge Dairy'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Maple Ridge Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Weekly Health Check'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Quality Assessment' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Quality Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the minimum milk production recorded?", "output": "SELECT MIN(milk) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Meadowbrook Farm' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Golden Fields Farm'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List pens by housing system type", "output": "SELECT housingSystemType, COUNT(*) as pen_count FROM pen GROUP BY housingSystemType;", "category": "pen_queries"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show diets used at each site", "output": "SELECT s.siteName, d.name as diet_name, d.numberOfAnimals FROM site s JOIN diets d ON s.id = d.siteId;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Valley View Farm'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Valley View Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of farm visits?", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Meadowbrook Farm'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Meadowbrook Farm'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show facility details for 'Hillside Dairy Co'", "output": "SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Hillside Dairy Co';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Welfare Inspection'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Welfare Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Herd Evaluation'?", "output": "SELECT * FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Green Valley Dairy'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Sunrise Dairy Farm'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Meadowbrook Farm'", "output": "SELECT * FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'Golden Fields Farm'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Golden Fields Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all customers in the United States", "output": "SELECT businessName, city, state FROM customer WHERE country = 'United States';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Feed Analysis'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What visits were conducted at sites owned by 'Green Valley Dairy'?", "output": "SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Green Valley Dairy';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Golden Fields Farm' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'System Evaluation'?", "output": "SELECT * FROM visit WHERE visitName = 'System Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Hillside Dairy Co' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of sites?", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show facility details for 'Prairie View Dairy'", "output": "SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Prairie View Dairy';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show average milk production per pen by customer", "output": "SELECT c.businessName, AVG(p.milk) as avg_pen_milk FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Heritage Dairy' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Heritage Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Herd Evaluation'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all completed visits", "output": "SELECT visitName, visitDate FROM visit WHERE visitStatus = 'completed';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Cloverfield Farm'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Cloverfield Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Performance Review'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Performance Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Heritage Dairy'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Heritage Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Heritage Dairy' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Heritage Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was 'Maple Ridge Dairy' last visited?", "output": "SELECT dateOfLastVisit FROM customer WHERE businessName = 'Maple Ridge Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Production Review'?", "output": "SELECT * FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many animal enclosures do we have?", "output": "SELECT COUNT(*) FROM pen;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Heritage Dairy'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Heritage Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Heritage Dairy'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Heritage Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Hillside Dairy Co'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Sunrise Dairy Farm'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find sites with ration cost above $5", "output": "SELECT siteName, rationCost FROM site WHERE rationCost > 5.0;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many diets are configured?", "output": "SELECT COUNT(*) FROM diets;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Herd <PERSON>'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Welfare Inspection'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Welfare Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Prairie View Dairy'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of observations?", "output": "SELECT COUNT(*) FROM noteBook;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average milk protein percentage?", "output": "SELECT AVG(milkProteinPercent) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all published visits", "output": "SELECT visitName, visitPublishedDate FROM visit WHERE isVisitAutoPublished = 1;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Hillside Dairy Co'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with milk protein above 3.2%", "output": "SELECT siteName, milkProteinPercent FROM site WHERE milkProteinPercent > 3.2;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of animal pens?", "output": "SELECT COUNT(*) FROM pen;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Feed Analysis'?", "output": "SELECT * FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Mountain View Ranch'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Mountain View Ranch';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List all favourite customers", "output": "SELECT businessName FROM customer WHERE favourite = 1;", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Facility Audit'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Facility Audit';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with low bacteria cell count", "output": "SELECT siteName, bacteriaCellCount FROM site WHERE bacteriaCellCount < 10000;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Which sites have milk fat percentage above 3.5%?", "output": "SELECT siteName, milkFatPercent FROM site WHERE milkFatPercent > 3.5;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average dry matter intake across sites?", "output": "SELECT AVG(dryMatterIntake) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count pens by housing system type", "output": "SELECT housingSystemType, COUNT(*) as pen_count FROM pen GROUP BY housingSystemType;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Cloverfield Farm' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Cloverfield Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Health Inspection'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Prairie View Dairy'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Hillside Dairy Co'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Feed Analysis' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find visits with heat stress issues", "output": "SELECT visitName, heatStress FROM visit WHERE heatStress IS NOT NULL;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many documentation entries exist?", "output": "SELECT COUNT(*) FROM noteBook;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Meadowbrook Farm'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all animal classifications", "output": "SELECT className, en_class, en_subClass FROM animalClass;", "category": "animal_class"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Golden Fields Farm' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show visits with poor locomotion scores", "output": "SELECT visitName, locomotionScore FROM visit WHERE locomotionScore < 2;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Valley View Farm' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Valley View Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many dairy businesses are registered?", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Nutrition Assessment'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Sunrise Dairy Farm'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show all animal types in English", "output": "SELECT en_class, en_subClass FROM animalClass;", "category": "animal_class"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Maple Ridge Dairy'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Maple Ridge Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Riverside Dairy' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Riverside Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Riverside Dairy' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Riverside Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Sunrise Dairy Farm' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Feed Analysis'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Maple Ridge Dairy' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Maple Ridge Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Mountain View Ranch'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Mountain View Ranch';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find sites with declining performance", "output": "SELECT siteName, milk, milkFatPercent FROM site WHERE milk < (SELECT AVG(milk) FROM site);", "category": "advanced_analytics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the status of visit 'Reproductive Check'?", "output": "SELECT visitStatus FROM visit WHERE visitName = 'Reproductive Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Valley View Farm' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Valley View Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of feeding programs?", "output": "SELECT COUNT(*) FROM diets;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show pens with high milk production", "output": "SELECT name, milk FROM pen WHERE milk > 1000;", "category": "pen_queries"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Golden Fields Farm' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find customers by postal code 12345", "output": "SELECT businessName, street, city FROM customer WHERE postalCode = '12345';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Heritage Dairy'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Heritage Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the number of facilities for 'Maple Ridge Dairy'", "output": "SELECT siteCount FROM customer WHERE businessName = 'Maple Ridge Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Maple Ridge Dairy'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Maple Ridge Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Feed Analysis'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Feed Analysis';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Heritage Dairy' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Heritage Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show notes for each customer", "output": "SELECT c.businessName, n.title, n.note FROM customer c JOIN noteBook n ON c.id = n.accountId;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Meadowbrook Farm'", "output": "SELECT contacts FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average number of lactating animals per site?", "output": "SELECT AVG(lactatingAnimal) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show facility details for 'Sunrise Dairy Farm'", "output": "SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Sunrise Dairy Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Riverside Dairy'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Riverside Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Mountain View Ranch' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Mountain View Ranch';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Which sites have more than 500 lactating animals?", "output": "SELECT siteName, lactatingAnimal FROM site WHERE lactatingAnimal > 500;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the account type of 'Riverside Dairy'?", "output": "SELECT accountType FROM customer WHERE businessName = 'Riverside Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Nutrition Assessment'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Which customers use the most diet programs?", "output": "SELECT c.businessName, COUNT(d.id) as diet_count FROM customer c JOIN site s ON c.id = s.accountId JOIN diets d ON s.id = d.siteId GROUP BY c.businessName ORDER BY diet_count DESC;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'Production Review'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'Production Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List animal classes in French", "output": "SELECT fr_class, fr_subClass FROM animalClass;", "category": "animal_class"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Riverside Dairy'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Riverside Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average days in milk for pens?", "output": "SELECT AVG(daysInMilk) FROM pen;", "category": "pen_queries"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Prairie View Dairy' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Health Inspection' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'Reproductive Check'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'Reproductive Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Total number of site visits", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "List customers with account type 1", "output": "SELECT businessName FROM customer WHERE accountType = 1;", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Performance Review'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Performance Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Weekly Health Check'?", "output": "SELECT * FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the body condition score in 'System Evaluation'?", "output": "SELECT bodyCondition FROM visit WHERE visitName = 'System Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Nutrition Assessment'?", "output": "SELECT * FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show sites with milk price above $20", "output": "SELECT siteName, currentMilkPrice FROM site WHERE currentMilkPrice > 20;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Health Inspection'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "When was visit 'Performance Review' conducted?", "output": "SELECT visitDate FROM visit WHERE visitName = 'Performance Review';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Cloverfield Farm' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Cloverfield Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Is 'Maple Ridge Dairy' marked as favourite?", "output": "SELECT favourite FROM customer WHERE businessName = 'Maple Ridge Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What are the production metrics for 'Meadowbrook Farm' sites?", "output": "SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Meadowbrook Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Nutrition Assessment'", "output": "SELECT * FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Sunrise Dairy Farm' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Cloverfield Farm'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Cloverfield Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me the contact information for 'Sunrise Dairy Farm'", "output": "SELECT contacts FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites do I have?", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the maximum milk production recorded?", "output": "SELECT MAX(milk) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all details for customer 'Cloverfield Farm'", "output": "SELECT * FROM customer WHERE businessName = 'Cloverfield Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show the locomotion score for 'System Evaluation'", "output": "SELECT locomotionScore FROM visit WHERE visitName = 'System Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Find visits with high rumen health scores", "output": "SELECT visitName, rumenHealth FROM visit WHERE rumenHealth > 3;", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Green Valley Dairy' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Golden Fields Farm'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Count all dairy farm sites", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Prairie View Dairy' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What are the production metrics for 'Green Valley Dairy' sites?", "output": "SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Green Valley Dairy';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Weekly Health Check'", "output": "SELECT * FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}]