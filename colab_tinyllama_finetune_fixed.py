#!/usr/bin/env python3
"""
FIXED Google Colab TinyLlama Fine-tuning for Dairy Database
Handles common tokenization and training errors
"""

import json
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    BitsAndBytesConfig
)
from datasets import Dataset as HFDataset
import numpy as np
from typing import Dict, List
import os
import gc
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
import warnings
warnings.filterwarnings("ignore")

class FixedColabTinyLlamaFineTuner:
    def __init__(self, model_name: str = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"):
        self.model_name = model_name
        self.tokenizer = None
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        print("🐄 FIXED Google Colab TinyLlama Fine-tuner")
        print("=" * 50)
        print(f"📱 Device: {self.device}")
        print(f"🧠 Model: {self.model_name}")
        
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            print(f"🔥 GPU: {gpu_name}")
            print(f"💾 GPU Memory: {gpu_memory:.1f} GB")
        else:
            print("⚠️ No GPU detected! Training will be very slow.")
    
    def setup_quantization_config(self):
        """Setup 4-bit quantization for memory efficiency"""
        return BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.bfloat16 if torch.cuda.is_available() else torch.float32
        )
    
    def setup_lora_config(self):
        """Setup LoRA configuration for efficient fine-tuning"""
        return LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=16,
            lora_alpha=32,
            lora_dropout=0.1,
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
        )
    
    def load_model_and_tokenizer(self, use_quantization: bool = True):
        """Load TinyLlama model and tokenizer with error handling"""
        print("\n📥 Loading TinyLlama model and tokenizer...")
        
        try:
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
            
            # Load model with quantization if GPU available
            if use_quantization and torch.cuda.is_available():
                print("⚡ Loading with 4-bit quantization...")
                quantization_config = self.setup_quantization_config()
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    quantization_config=quantization_config,
                    device_map="auto",
                    trust_remote_code=True,
                    torch_dtype=torch.bfloat16
                )
                # Setup for training
                self.model = prepare_model_for_kbit_training(self.model)
            else:
                print("📱 Loading without quantization...")
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    device_map="auto" if torch.cuda.is_available() else None,
                    trust_remote_code=True,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
                )
            
            # Apply LoRA
            lora_config = self.setup_lora_config()
            self.model = get_peft_model(self.model, lora_config)
            
            print("✅ Model loaded successfully!")
            
            # Print trainable parameters
            trainable_params = 0
            all_param = 0
            for _, param in self.model.named_parameters():
                all_param += param.numel()
                if param.requires_grad:
                    trainable_params += param.numel()
            
            print(f"🎯 Trainable params: {trainable_params:,}")
            print(f"📊 All params: {all_param:,}")
            print(f"📈 Trainable%: {100 * trainable_params / all_param:.2f}%")
            
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            print("🔄 Trying fallback approach...")
            self.load_model_fallback()
    
    def load_model_fallback(self):
        """Fallback model loading without quantization"""
        print("📱 Loading model without advanced features...")
        
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
        
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_name,
            torch_dtype=torch.float32,
            trust_remote_code=True
        )
        
        # Simple LoRA config for fallback
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=8,
            lora_alpha=16,
            target_modules=["q_proj", "v_proj"]
        )
        self.model = get_peft_model(self.model, lora_config)
        print("✅ Fallback model loaded!")
    
    def format_prompt(self, instruction: str, input_text: str, output: str = None) -> str:
        """Format prompt for TinyLlama chat format"""
        if output:
            return f"""<|system|>
You are a helpful AI assistant that generates SQL queries for dairy farm database questions.

<|user|>
{instruction}

Question: {input_text}

<|assistant|>
{output}"""
        else:
            return f"""<|system|>
You are a helpful AI assistant that generates SQL queries for dairy farm database questions.

<|user|>
{instruction}

Question: {input_text}

<|assistant|>
"""
    
    def load_and_prepare_dataset(self, dataset_path: str):
        """Load and prepare the training dataset with error handling"""
        print(f"\n📊 Loading dataset from: {dataset_path}")
        
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"📈 Total samples: {len(data)}")
            
            # Format data for TinyLlama
            formatted_data = []
            for i, item in enumerate(data):
                try:
                    formatted_text = self.format_prompt(
                        item['instruction'], 
                        item['input'], 
                        item['output']
                    )
                    formatted_data.append({'text': formatted_text})
                except Exception as e:
                    print(f"⚠️ Skipping sample {i}: {e}")
                    continue
            
            print(f"✅ Formatted {len(formatted_data)} samples")
            
            # Split into train/validation (90/10)
            split_idx = int(0.9 * len(formatted_data))
            train_data = formatted_data[:split_idx]
            val_data = formatted_data[split_idx:]
            
            print(f"🏋️ Training samples: {len(train_data)}")
            print(f"🧪 Validation samples: {len(val_data)}")
            
            return train_data, val_data
            
        except Exception as e:
            print(f"❌ Error loading dataset: {e}")
            return [], []
    
    def preprocess_function(self, examples):
        """Preprocess function for tokenization"""
        # Handle both single examples and batches
        if isinstance(examples["text"], str):
            texts = [examples["text"]]
        else:
            texts = examples["text"]
        
        # Tokenize
        model_inputs = self.tokenizer(
            texts,
            truncation=True,
            padding=False,
            max_length=512,
            return_overflowing_tokens=False,
        )
        
        # For causal LM, labels are the same as input_ids
        model_inputs["labels"] = model_inputs["input_ids"].copy()
        
        return model_inputs
    
    def fine_tune(self, dataset_path: str, output_dir: str = "./tinyllama-dairy-sql"):
        """Fine-tune TinyLlama with comprehensive error handling"""
        
        try:
            # Load model and tokenizer
            self.load_model_and_tokenizer(use_quantization=torch.cuda.is_available())
            
            # Load and prepare dataset
            train_data, val_data = self.load_and_prepare_dataset(dataset_path)
            
            if not train_data:
                print("❌ No training data available!")
                return None
            
            # Convert to HuggingFace datasets
            train_dataset = HFDataset.from_list(train_data)
            val_dataset = HFDataset.from_list(val_data) if val_data else None
            
            # Tokenize datasets
            print("\n🔤 Tokenizing datasets...")
            train_dataset = train_dataset.map(
                self.preprocess_function,
                batched=True,
                remove_columns=train_dataset.column_names
            )
            
            if val_dataset:
                val_dataset = val_dataset.map(
                    self.preprocess_function,
                    batched=True,
                    remove_columns=val_dataset.column_names
                )
            
            # Data collator
            data_collator = DataCollatorForLanguageModeling(
                tokenizer=self.tokenizer,
                mlm=False,
            )
            
            # Training arguments with conservative settings
            training_args = TrainingArguments(
                output_dir=output_dir,
                num_train_epochs=2,  # Reduced epochs
                per_device_train_batch_size=2 if torch.cuda.is_available() else 1,
                per_device_eval_batch_size=2 if torch.cuda.is_available() else 1,
                gradient_accumulation_steps=8,  # Increased to maintain effective batch size
                warmup_steps=50,
                max_steps=500,  # Reduced steps
                learning_rate=1e-4,  # Lower learning rate
                fp16=torch.cuda.is_available(),
                logging_steps=25,
                optim="adamw_torch",  # More compatible optimizer
                evaluation_strategy="steps" if val_dataset else "no",
                eval_steps=100 if val_dataset else None,
                save_steps=100,
                save_total_limit=2,
                load_best_model_at_end=bool(val_dataset),
                ddp_find_unused_parameters=False,
                group_by_length=True,
                report_to=None,
                run_name="tinyllama-dairy-sql-fixed",
                dataloader_pin_memory=False,
                remove_unused_columns=False,
            )
            
            # Initialize trainer
            trainer = Trainer(
                model=self.model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset,
                tokenizer=self.tokenizer,
                data_collator=data_collator,
            )
            
            # Clear cache before training
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()
            
            # Start training
            print("\n🚀 Starting fine-tuning...")
            print("📊 Training progress:")
            print("-" * 50)
            
            trainer.train()
            
            # Save the final model
            print("\n💾 Saving model...")
            trainer.save_model()
            self.tokenizer.save_pretrained(output_dir)
            
            print(f"✅ Model saved to: {output_dir}")
            return trainer
            
        except Exception as e:
            print(f"❌ Training error: {e}")
            print("🔄 Trying with reduced settings...")
            return self.fine_tune_minimal(dataset_path, output_dir)
    
    def fine_tune_minimal(self, dataset_path: str, output_dir: str):
        """Minimal fine-tuning with basic settings"""
        print("🔧 Running minimal fine-tuning...")
        
        # Very basic training arguments
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=1,
            per_device_train_batch_size=1,
            gradient_accumulation_steps=16,
            max_steps=100,
            learning_rate=5e-5,
            logging_steps=10,
            save_steps=50,
            fp16=False,
            evaluation_strategy="no",
            report_to=None,
        )
        
        # Load data again
        train_data, _ = self.load_and_prepare_dataset(dataset_path)
        train_dataset = HFDataset.from_list(train_data)
        train_dataset = train_dataset.map(self.preprocess_function, batched=True)
        
        data_collator = DataCollatorForLanguageModeling(tokenizer=self.tokenizer, mlm=False)
        
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            tokenizer=self.tokenizer,
            data_collator=data_collator,
        )
        
        trainer.train()
        trainer.save_model()
        self.tokenizer.save_pretrained(output_dir)
        
        print(f"✅ Minimal training completed! Model saved to: {output_dir}")
        return trainer

def main():
    """Main function for Google Colab with error handling"""
    print("🐄 FIXED TinyLlama Dairy SQL Fine-tuning - Google Colab")
    print("=" * 70)
    
    try:
        # Initialize fine-tuner
        fine_tuner = FixedColabTinyLlamaFineTuner()
        
        # Check if dataset exists
        dataset_file = "extensive_dairy_sql_dataset.json"
        if not os.path.exists(dataset_file):
            print(f"❌ Dataset file {dataset_file} not found!")
            print("Please make sure you ran the dataset generation cell first.")
            return
        
        # Fine-tune the model
        print("\n🚀 Starting fine-tuning process...")
        trainer = fine_tuner.fine_tune(dataset_file)
        
        if trainer:
            print("\n🎉 Fine-tuning completed successfully!")
            print("📁 Model saved to: ./tinyllama-dairy-sql")
        else:
            print("❌ Fine-tuning failed!")
            
    except Exception as e:
        print(f"❌ Main error: {e}")
        print("🔄 Please check the error and try again.")

if __name__ == "__main__":
    main()
