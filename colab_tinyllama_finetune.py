#!/usr/bin/env python3
"""
Google Colab Optimized TinyLlama Fine-tuning for Dairy Database
Fine-tunes TinyLlama 1.1B for text-to-SQL tasks on dairy farm data
Optimized for Google Colab's GPU environment
"""

import json
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    BitsAndBytesConfig
)
from datasets import Dataset as HFDataset
import numpy as np
from typing import Dict, List
import os
import gc
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
import warnings
warnings.filterwarnings("ignore")

class ColabTinyLlamaFineTuner:
    def __init__(self, model_name: str = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"):
        self.model_name = model_name
        self.tokenizer = None
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        print("🐄 Google Colab TinyLlama Fine-tuner")
        print("=" * 50)
        print(f"📱 Device: {self.device}")
        print(f"🧠 Model: {self.model_name}")
        
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            print(f"🔥 GPU: {gpu_name}")
            print(f"💾 GPU Memory: {gpu_memory:.1f} GB")
        else:
            print("⚠️ No GPU detected! Training will be very slow.")
    
    def setup_quantization_config(self):
        """Setup 4-bit quantization for memory efficiency"""
        return BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.bfloat16 if torch.cuda.is_available() else torch.float32
        )
    
    def setup_lora_config(self):
        """Setup LoRA configuration for efficient fine-tuning"""
        return LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=16,  # Rank
            lora_alpha=32,
            lora_dropout=0.1,
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
        )
    
    def load_model_and_tokenizer(self, use_quantization: bool = True):
        """Load TinyLlama model and tokenizer with Colab optimizations"""
        print("\n📥 Loading TinyLlama model and tokenizer...")
        
        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        
        # Add padding token if not present
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
        
        # Load model with quantization if GPU available
        if use_quantization and torch.cuda.is_available():
            print("⚡ Loading with 4-bit quantization...")
            quantization_config = self.setup_quantization_config()
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                quantization_config=quantization_config,
                device_map="auto",
                trust_remote_code=True,
                torch_dtype=torch.bfloat16
            )
            # Setup for training
            self.model = prepare_model_for_kbit_training(self.model)
        else:
            print("📱 Loading without quantization...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
            )
        
        # Apply LoRA
        lora_config = self.setup_lora_config()
        self.model = get_peft_model(self.model, lora_config)
        
        print("✅ Model loaded successfully!")
        
        # Print trainable parameters
        trainable_params = 0
        all_param = 0
        for _, param in self.model.named_parameters():
            all_param += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()
        
        print(f"🎯 Trainable params: {trainable_params:,}")
        print(f"📊 All params: {all_param:,}")
        print(f"📈 Trainable%: {100 * trainable_params / all_param:.2f}%")
    
    def format_prompt(self, instruction: str, input_text: str, output: str = None) -> str:
        """Format prompt for TinyLlama chat format"""
        if output:
            # Training format
            return f"""<|system|>
You are a helpful AI assistant that generates SQL queries for dairy farm database questions.

<|user|>
{instruction}

Question: {input_text}

<|assistant|>
{output}"""
        else:
            # Inference format
            return f"""<|system|>
You are a helpful AI assistant that generates SQL queries for dairy farm database questions.

<|user|>
{instruction}

Question: {input_text}

<|assistant|>
"""
    
    def load_and_prepare_dataset(self, dataset_path: str):
        """Load and prepare the training dataset"""
        print(f"\n📊 Loading dataset from: {dataset_path}")
        
        with open(dataset_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📈 Total samples: {len(data)}")
        
        # Show dataset statistics
        categories = {}
        for item in data:
            cat = item.get('category', 'general')
            categories[cat] = categories.get(cat, 0) + 1
        
        print("📋 Dataset breakdown:")
        for cat, count in categories.items():
            print(f"   • {cat}: {count} samples")
        
        # Format data for TinyLlama
        formatted_data = []
        for item in data:
            formatted_text = self.format_prompt(
                item['instruction'], 
                item['input'], 
                item['output']
            )
            formatted_data.append({
                'text': formatted_text,
                'category': item.get('category', 'general')
            })
        
        # Split into train/validation (90/10)
        split_idx = int(0.9 * len(formatted_data))
        train_data = formatted_data[:split_idx]
        val_data = formatted_data[split_idx:]
        
        print(f"🏋️ Training samples: {len(train_data)}")
        print(f"🧪 Validation samples: {len(val_data)}")
        
        return train_data, val_data
    
    def tokenize_function(self, examples):
        """Tokenize the dataset"""
        tokenized = self.tokenizer(
            examples["text"],
            truncation=True,
            padding=False,
            max_length=512,
            return_overflowing_tokens=False,
        )
        
        # For causal LM, labels are the same as input_ids
        tokenized["labels"] = tokenized["input_ids"].copy()
        
        return tokenized
    
    def fine_tune(self, dataset_path: str, output_dir: str = "./tinyllama-dairy-sql"):
        """Fine-tune TinyLlama on dairy SQL dataset"""
        
        # Load model and tokenizer
        self.load_model_and_tokenizer(use_quantization=torch.cuda.is_available())
        
        # Load and prepare dataset
        train_data, val_data = self.load_and_prepare_dataset(dataset_path)
        
        # Convert to HuggingFace datasets
        train_dataset = HFDataset.from_list(train_data)
        val_dataset = HFDataset.from_list(val_data)
        
        # Tokenize datasets
        print("\n🔤 Tokenizing datasets...")
        train_dataset = train_dataset.map(
            self.tokenize_function,
            batched=True,
            remove_columns=train_dataset.column_names
        )
        val_dataset = val_dataset.map(
            self.tokenize_function,
            batched=True,
            remove_columns=val_dataset.column_names
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
        )
        
        # Training arguments optimized for Colab
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=3,
            per_device_train_batch_size=4 if torch.cuda.is_available() else 1,
            per_device_eval_batch_size=4 if torch.cuda.is_available() else 1,
            gradient_accumulation_steps=4,
            warmup_steps=100,
            max_steps=1000,
            learning_rate=2e-4,
            fp16=torch.cuda.is_available(),
            logging_steps=25,
            optim="paged_adamw_32bit" if torch.cuda.is_available() else "adamw_torch",
            evaluation_strategy="steps",
            eval_steps=100,
            save_steps=200,
            save_total_limit=2,
            load_best_model_at_end=True,
            ddp_find_unused_parameters=False,
            group_by_length=True,
            report_to=None,
            run_name="tinyllama-dairy-sql-colab",
            dataloader_pin_memory=False,  # Better for Colab
        )
        
        # Initialize trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            tokenizer=self.tokenizer,
            data_collator=data_collator,
        )
        
        # Clear cache before training
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        # Start training
        print("\n🚀 Starting fine-tuning...")
        print("📊 Training progress will be shown below:")
        print("-" * 50)
        
        trainer.train()
        
        # Save the final model
        print("\n💾 Saving model...")
        trainer.save_model()
        self.tokenizer.save_pretrained(output_dir)
        
        print(f"✅ Model saved to: {output_dir}")
        
        return trainer
    
    def test_model(self, model_path: str, test_questions: List[str]):
        """Test the fine-tuned model"""
        print(f"\n🧪 Testing model from: {model_path}")
        
        # Load the fine-tuned model
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            device_map="auto" if torch.cuda.is_available() else None,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
        )
        
        print("\n🔍 Testing the model:")
        print("=" * 60)
        
        for i, question in enumerate(test_questions, 1):
            prompt = self.format_prompt(
                "Generate SQL query for the following question about dairy farm data:",
                question
            )
            
            inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
            if torch.cuda.is_available():
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=100,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                )
            
            # Decode only the new tokens
            generated_text = tokenizer.decode(outputs[0][inputs['input_ids'].shape[1]:], skip_special_tokens=True)
            
            print(f"\n{i}. ❓ Question: {question}")
            print(f"   🔍 Generated SQL: {generated_text.strip()}")
            print("-" * 40)

def main():
    """Main function for Google Colab"""
    print("🐄 TinyLlama Dairy SQL Fine-tuning - Google Colab Edition")
    print("=" * 70)
    
    # Initialize fine-tuner
    fine_tuner = ColabTinyLlamaFineTuner()
    
    # Check if dataset exists, if not generate it
    dataset_file = "extensive_dairy_sql_dataset.json"
    if not os.path.exists(dataset_file):
        print("\n📊 Generating training dataset...")
        try:
            from dataset_generator import ExtensiveDairyDatasetGenerator
            generator = ExtensiveDairyDatasetGenerator()
            generator.save_dataset(dataset_file)
        except ImportError:
            print("❌ dataset_generator.py not found. Please upload it first!")
            return
    
    # Fine-tune the model
    print("\n🚀 Starting fine-tuning process...")
    trainer = fine_tuner.fine_tune(dataset_file)
    
    # Test the model
    test_questions = [
        "How many sites do I have?",
        "How many sites does Sunrise Dairy Farm have?",
        "What data is inside visit 'Health Check'?",
        "What is the average milk production?",
        "Show me all customers in Canada",
        "Which sites have milk fat percentage above 3.5%?"
    ]
    
    fine_tuner.test_model("./tinyllama-dairy-sql", test_questions)
    
    print("\n🎉 Fine-tuning completed successfully!")
    print("📁 Model saved to: ./tinyllama-dairy-sql")
    print("\n💾 To download your model:")
    print("   1. Zip the model folder")
    print("   2. Download from Colab files panel")

if __name__ == "__main__":
    main()
