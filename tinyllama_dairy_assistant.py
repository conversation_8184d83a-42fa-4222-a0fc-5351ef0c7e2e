#!/usr/bin/env python3
"""
TinyLlama Dairy SQL Assistant - Inference Script
Uses the fine-tuned TinyLlama model to generate SQL queries from natural language questions
Optimized for offline use with your dairy database
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import sqlite3
import pandas as pd
from typing import List, Dict, Optional
import json
import os
import time
import warnings
warnings.filterwarnings("ignore")

class TinyLlamaDairyAssistant:
    def __init__(self, model_path: str = "./tinyllama-dairy-sql", db_path: str = None):
        """
        Initialize the TinyLlama Dairy SQL Assistant
        
        Args:
            model_path: Path to the fine-tuned TinyLlama model
            db_path: Path to the SQLite database file
        """
        self.model_path = model_path
        self.db_path = db_path
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.tokenizer = None
        self.model = None
        self.db_connection = None
        
        print(f"🐄 TinyLlama Dairy SQL Assistant")
        print(f"📱 Device: {self.device}")
        print(f"🧠 Model path: {model_path}")
        
        self.load_model()
        
        if db_path and os.path.exists(db_path):
            self.connect_database()
    
    def load_model(self):
        """Load the fine-tuned TinyLlama model and tokenizer"""
        try:
            print(f"📥 Loading fine-tuned model from: {self.model_path}")
            
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                device_map="auto",
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                trust_remote_code=True
            )
            
            self.model.eval()
            print("✅ Fine-tuned model loaded successfully!")
            
        except Exception as e:
            print(f"❌ Error loading fine-tuned model: {e}")
            print("🔄 Falling back to base TinyLlama model...")
            self.load_base_model()
    
    def load_base_model(self):
        """Load the base TinyLlama model if fine-tuned model is not available"""
        model_name = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
        print(f"📥 Loading base model: {model_name}")
        
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_name,
            device_map="auto",
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            trust_remote_code=True
        )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print("✅ Base model loaded successfully!")
    
    def connect_database(self):
        """Connect to the SQLite database"""
        try:
            self.db_connection = sqlite3.connect(self.db_path)
            print(f"🗄️ Connected to database: {self.db_path}")
        except Exception as e:
            print(f"❌ Error connecting to database: {e}")
    
    def format_prompt(self, question: str) -> str:
        """Format prompt for TinyLlama chat format"""
        return f"""<|system|>
You are a helpful AI assistant that generates SQL queries for dairy farm database questions.

<|user|>
Generate SQL query for the following question about dairy farm data:

Question: {question}

<|assistant|>
"""
    
    def generate_sql(self, question: str, temperature: float = 0.7, max_new_tokens: int = 100) -> str:
        """
        Generate SQL query from natural language question
        
        Args:
            question: Natural language question
            temperature: Sampling temperature (0.0 = deterministic, 1.0 = random)
            max_new_tokens: Maximum number of new tokens to generate
            
        Returns:
            Generated SQL query
        """
        # Format prompt
        prompt = self.format_prompt(question)
        
        # Tokenize input
        inputs = self.tokenizer(
            prompt, 
            return_tensors="pt", 
            truncation=True,
            max_length=512,
            padding=True
        )
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Generate SQL
        start_time = time.time()
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_new_tokens,
                temperature=temperature,
                do_sample=True if temperature > 0 else False,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                repetition_penalty=1.1,
                top_p=0.9,
                top_k=50
            )
        
        generation_time = time.time() - start_time
        
        # Decode only the new tokens (response)
        generated_text = self.tokenizer.decode(
            outputs[0][inputs['input_ids'].shape[1]:], 
            skip_special_tokens=True
        )
        
        # Clean up the generated SQL
        sql = self.clean_sql(generated_text)
        
        print(f"⚡ Generation time: {generation_time:.2f}s")
        return sql
    
    def clean_sql(self, sql: str) -> str:
        """Clean and format the generated SQL"""
        # Remove extra whitespace and newlines
        sql = " ".join(sql.split())
        
        # Remove common artifacts
        sql = sql.replace("<|assistant|>", "").strip()
        sql = sql.replace("<|user|>", "").strip()
        sql = sql.replace("<|system|>", "").strip()
        
        # Extract SQL if there's extra text
        if "SELECT" in sql.upper():
            # Find the first SELECT statement
            start_idx = sql.upper().find("SELECT")
            sql = sql[start_idx:]
            
            # Find the end of the SQL statement
            if ";" in sql:
                end_idx = sql.find(";") + 1
                sql = sql[:end_idx]
        
        # Ensure SQL ends with semicolon
        if sql and not sql.endswith(';'):
            sql += ';'
        
        return sql.strip()
    
    def execute_sql(self, sql: str) -> Optional[pd.DataFrame]:
        """
        Execute SQL query on the database
        
        Args:
            sql: SQL query to execute
            
        Returns:
            Query results as pandas DataFrame or None if error
        """
        if not self.db_connection:
            print("❌ No database connection available")
            return None
        
        try:
            result = pd.read_sql_query(sql, self.db_connection)
            return result
        except Exception as e:
            print(f"❌ Error executing SQL: {e}")
            return None
    
    def ask_question(self, question: str, execute: bool = True) -> Dict:
        """
        Ask a question and get both SQL and results
        
        Args:
            question: Natural language question
            execute: Whether to execute the SQL on database
            
        Returns:
            Dictionary with question, generated SQL, and results
        """
        print(f"\n🤔 Question: {question}")
        
        # Generate SQL
        sql = self.generate_sql(question)
        print(f"🔍 Generated SQL: {sql}")
        
        result = {
            'question': question,
            'sql': sql,
            'results': None,
            'error': None,
            'execution_time': None
        }
        
        # Execute SQL if requested and database is available
        if execute and self.db_connection and sql:
            try:
                start_time = time.time()
                results = self.execute_sql(sql)
                execution_time = time.time() - start_time
                
                if results is not None:
                    result['results'] = results
                    result['execution_time'] = execution_time
                    print(f"✅ Results ({len(results)} rows) in {execution_time:.3f}s:")
                    if len(results) > 0:
                        print(results.to_string(index=False, max_rows=10))
                    else:
                        print("No results found.")
                else:
                    result['error'] = "Failed to execute SQL"
                    print("❌ Failed to execute SQL")
            except Exception as e:
                result['error'] = str(e)
                print(f"❌ Error: {e}")
        
        return result
    
    def interactive_mode(self):
        """Start interactive question-answering mode"""
        print("\n" + "="*70)
        print("🐄 TINYLLAMA DAIRY SQL ASSISTANT - Interactive Mode")
        print("="*70)
        print("Ask questions about your dairy farm data!")
        print("Commands:")
        print("  • Type your question and press Enter")
        print("  • 'help' - Show example questions")
        print("  • 'stats' - Show model statistics")
        print("  • 'quit' or 'exit' - Stop the assistant")
        print("-"*70)
        
        question_count = 0
        total_time = 0
        
        while True:
            try:
                question = input("\n💬 Your question: ").strip()
                
                if question.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    if question_count > 0:
                        print(f"📊 Session stats: {question_count} questions, avg time: {total_time/question_count:.2f}s")
                    break
                
                if question.lower() == 'help':
                    self.show_help()
                    continue
                
                if question.lower() == 'stats':
                    self.show_stats()
                    continue
                
                if not question:
                    continue
                
                # Process the question
                start_time = time.time()
                result = self.ask_question(question)
                end_time = time.time()
                
                question_count += 1
                total_time += (end_time - start_time)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def show_help(self):
        """Show example questions"""
        examples = [
            "How many sites do I have?",
            "How many sites does Sunrise Dairy Farm have?",
            "What data is inside visit 'Health Check'?",
            "What is the average milk production across all sites?",
            "Which sites have milk fat percentage above 3.5%?",
            "Show me all customers in Canada",
            "What visits were conducted last month?",
            "How many lactating animals do we have in total?",
            "Find sites with high somatic cell count",
            "Show the top 5 sites by milk production"
        ]
        
        print("\n📝 Example questions you can ask:")
        print("-" * 50)
        for i, example in enumerate(examples, 1):
            print(f"{i:2d}. {example}")
    
    def show_stats(self):
        """Show model and system statistics"""
        print("\n📊 System Statistics:")
        print("-" * 30)
        print(f"🧠 Model: TinyLlama 1.1B")
        print(f"📱 Device: {self.device}")
        print(f"💾 Model path: {self.model_path}")
        print(f"🗄️ Database: {'Connected' if self.db_connection else 'Not connected'}")
        
        if torch.cuda.is_available():
            print(f"🔥 GPU: {torch.cuda.get_device_name(0)}")
            print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

def main():
    """Main function to run the assistant"""
    import argparse
    
    parser = argparse.ArgumentParser(description="TinyLlama Dairy SQL Assistant")
    parser.add_argument("--model", default="./tinyllama-dairy-sql", help="Path to fine-tuned model")
    parser.add_argument("--database", help="Path to SQLite database file")
    parser.add_argument("--interactive", action="store_true", help="Start interactive mode")
    parser.add_argument("--question", help="Single question to ask")
    
    args = parser.parse_args()
    
    # Initialize assistant
    assistant = TinyLlamaDairyAssistant(
        model_path=args.model,
        db_path=args.database
    )
    
    if args.interactive:
        # Interactive mode
        assistant.interactive_mode()
    elif args.question:
        # Single question
        assistant.ask_question(args.question)
    else:
        # Demo mode with example questions
        demo_questions = [
            "How many sites do I have?",
            "How many sites does Sunrise Dairy Farm have?",
            "What is the average milk production?",
            "Show me all customers in Canada",
            "Which sites have milk fat percentage above 3.5%?"
        ]
        
        print("🚀 Running demo with example questions...")
        for question in demo_questions:
            assistant.ask_question(question, execute=False)
            print()

if __name__ == "__main__":
    main()
