# 🐄 Dairy Database LLM Fine-tuning Project

A complete solution for fine-tuning an LLM to understand your dairy farm database schema and answer natural language questions with SQL queries.

## 🎯 **What This Does**

This project fine-tunes a CodeT5+ model to:
- ✅ Answer questions like "How many sites do I have?"
- ✅ Generate SQL queries for your Cargill dairy database
- ✅ Understand dairy-specific terminology and relationships
- ✅ Work completely offline once trained
- ✅ Handle complex queries about customers, sites, visits, and production data

## 📁 **Project Structure**

```
Fine-tune-llm/
├── cargill.db (1).sql          # Your database schema
├── dataset_generator.py        # Generates training data
├── fine_tune_dairy_llm.py     # Main training script
├── dairy_sql_assistant.py     # Inference/chat interface
├── requirements.txt           # Python dependencies
├── FREE_TRAINING_PLATFORMS.md # Guide to free GPU platforms
└── README.md                 # This file
```

## 🚀 **Quick Start**

### 1. **Generate Training Dataset**
```bash
python dataset_generator.py
```
This creates `dairy_sql_dataset.json` with 50+ question-SQL pairs based on your schema.

### 2. **Fine-tune the Model**
```bash
python fine_tune_dairy_llm.py
```
This trains a CodeT5+ model on your dairy data (takes 30-60 minutes on free GPU).

### 3. **Use Your Trained Model**
```bash
# Interactive chat mode
python dairy_sql_assistant.py --interactive

# Single question
python dairy_sql_assistant.py --question "How many sites do I have?"

# With database connection
python dairy_sql_assistant.py --database "your_database.db" --interactive
```

## 🔧 **Installation**

### Local Setup:
```bash
# Create virtual environment
python -m venv dairy_llm_env
source dairy_llm_env/bin/activate  # Linux/Mac
# dairy_llm_env\Scripts\activate   # Windows

# Install dependencies
pip install -r requirements.txt
```

### Google Colab (Recommended):
1. Open [Google Colab](https://colab.research.google.com)
2. Upload all project files
3. Run: `!pip install -r requirements.txt`
4. Execute the training script

## 📊 **Training Dataset Examples**

The generated dataset includes questions like:

| Question | Generated SQL |
|----------|---------------|
| "How many sites do I have?" | `SELECT COUNT(*) FROM site;` |
| "How many sites does Sunrise Dairy Farm have?" | `SELECT siteCount FROM customer WHERE businessName = 'Sunrise Dairy Farm';` |
| "What data is inside visit 'Health Check'?" | `SELECT * FROM visit WHERE visitName = 'Health Check';` |
| "What is the average milk production?" | `SELECT AVG(milk) FROM site;` |
| "Which sites have milk fat above 3.5%?" | `SELECT siteName, milkFatPercent FROM site WHERE milkFatPercent > 3.5;` |

## 🎯 **Model Recommendations**

### **CodeT5+ 220M** (Recommended)
- **Size**: ~1GB
- **Memory**: 4GB VRAM
- **Training time**: 30-60 minutes
- **Perfect for**: Offline use, fast inference

### **CodeT5+ 770M** (Better Performance)
- **Size**: ~3GB  
- **Memory**: 8GB VRAM
- **Training time**: 1-2 hours
- **Better**: More accurate SQL generation

### **SQLCoder-7B** (Best Performance)
- **Size**: ~14GB
- **Memory**: 16GB+ VRAM
- **Training time**: 2-4 hours
- **Best**: Highest accuracy, needs powerful GPU

## 💰 **Free Training Options**

| Platform | GPU | Free Hours | Best For |
|----------|-----|------------|----------|
| **Google Colab** | T4 (16GB) | ~100/month | CodeT5+ 220M/770M |
| **Kaggle** | P100 (16GB) | 30/week | All models |
| **Paperspace** | Various | Limited | Testing |

See [FREE_TRAINING_PLATFORMS.md](FREE_TRAINING_PLATFORMS.md) for detailed setup instructions.

## 🔍 **Database Schema Understanding**

Your model will understand these key tables:

- **`customer`**: Dairy farm businesses and accounts
- **`site`**: Individual farm locations with production metrics  
- **`visit`**: Farm visits and assessments
- **`pen`**: Animal housing areas
- **`diets`**: Feed and nutrition programs
- **`animalClass`**: Animal categories (lactating, dry, heifer, etc.)

## 📈 **Expected Performance**

After fine-tuning, your model should achieve:
- ✅ **90%+ accuracy** on basic counting queries
- ✅ **85%+ accuracy** on customer-specific questions
- ✅ **80%+ accuracy** on complex join queries
- ✅ **Sub-second response time** for most queries

## 🛠 **Customization**

### Add More Training Data:
Edit `dataset_generator.py` to add more question types:

```python
def generate_custom_queries(self):
    custom_questions = [
        ("Your question here", "SELECT your_sql_here;"),
        # Add more...
    ]
    return custom_questions
```

### Adjust Training Parameters:
In `fine_tune_dairy_llm.py`:

```python
training_args = TrainingArguments(
    num_train_epochs=5,        # More epochs = better learning
    per_device_train_batch_size=2,  # Smaller = less memory
    learning_rate=5e-5,        # Learning rate
)
```

## 🔧 **Troubleshooting**

### Out of Memory Error:
```python
# Reduce batch size
per_device_train_batch_size=1
gradient_accumulation_steps=8

# Enable gradient checkpointing  
gradient_checkpointing=True
```

### Poor SQL Quality:
1. Add more training examples
2. Increase training epochs
3. Use a larger model (770M instead of 220M)
4. Improve data quality in dataset generator

### Model Not Loading:
```bash
# Check if model files exist
ls -la dairy-sql-model/

# Try base model first
python dairy_sql_assistant.py --model "Salesforce/codet5p-220m"
```

## 📝 **Example Usage**

```python
from dairy_sql_assistant import DairySQLAssistant

# Initialize assistant
assistant = DairySQLAssistant(
    model_path="./dairy-sql-model",
    db_path="your_database.db"
)

# Ask questions
result = assistant.ask_question("How many sites do I have?")
print(f"SQL: {result['sql']}")
print(f"Results: {result['results']}")

# Interactive mode
assistant.interactive_mode()
```

## 🎉 **Next Steps**

1. **Train your model** using Google Colab (free!)
2. **Test with your actual database**
3. **Add more training data** for better accuracy
4. **Deploy locally** for offline use
5. **Integrate** into your existing applications

## 📞 **Support**

If you need help:
1. Check the troubleshooting section
2. Review [FREE_TRAINING_PLATFORMS.md](FREE_TRAINING_PLATFORMS.md)
3. Ensure your database schema matches the training data

## 🏆 **Success Metrics**

Your fine-tuned model should be able to:
- ✅ Count sites, customers, visits
- ✅ Find specific customer information  
- ✅ Query production metrics
- ✅ Handle dairy-specific terminology
- ✅ Generate syntactically correct SQL
- ✅ Work completely offline

**Ready to get started? Run `python dataset_generator.py` to begin!** 🚀
