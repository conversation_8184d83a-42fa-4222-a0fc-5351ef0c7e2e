# 🚀 Complete Google Colab Setup for TinyLlama Dairy SQL Fine-tuning

## 📋 **What You'll Do**
1. Open Google Colab
2. Enable GPU
3. Copy-paste code cells
4. Wait 25-35 minutes for training
5. Download your trained model

## 🔧 **Step 1: Open Google Colab**

1. **Go to [colab.research.google.com](https://colab.research.google.com)**
2. **Sign in** with your Google account
3. **Click "New notebook"**

## ⚡ **Step 2: Enable GPU (CRITICAL!)**

**BEFORE DOING ANYTHING ELSE:**
1. Click **"Runtime"** → **"Change runtime type"**
2. Set **"Hardware accelerator"** to **"GPU"**
3. Click **"Save"**
4. **Verify GPU**: Run this in a cell:
```python
import torch
print(f"GPU Available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU Name: {torch.cuda.get_device_name(0)}")
```

## 📁 **Step 3: Upload Files**

**Method A: Upload via Code (Recommended)**
```python
# Cell 1: Upload Files
from google.colab import files
import os

print("📁 Upload your Python files one by one:")
print("1. dataset_generator.py")
print("2. colab_tinyllama_finetune.py") 
print("3. cargill.db (1).sql (optional)")

uploaded = files.upload()
print(f"✅ Uploaded files: {list(uploaded.keys())}")
```

**Method B: Manual Upload**
1. Click **folder icon** on left sidebar
2. Click **upload button**
3. Upload these files:
   - `dataset_generator.py`
   - `colab_tinyllama_finetune.py`

## 🚀 **Step 4: Complete Training Code**

Copy this into your Colab notebook (one cell per section):

### **Cell 1: Setup and Install Requirements**
```python
# 🐄 TinyLlama Dairy SQL Fine-tuning Setup
import torch
import subprocess
import sys

print("🚀 TinyLlama Dairy SQL Fine-tuning - Google Colab")
print("=" * 60)

# Check GPU
print(f"🔥 GPU Available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"🚀 GPU: {torch.cuda.get_device_name(0)}")
    print(f"💾 Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
else:
    print("⚠️ No GPU! Enable GPU in Runtime → Change runtime type")

# Install requirements
print("\n📦 Installing requirements...")
requirements = [
    "torch>=2.0.0",
    "transformers>=4.35.0", 
    "datasets>=2.14.0",
    "accelerate>=0.24.0",
    "peft>=0.6.0",
    "bitsandbytes>=0.41.0",
    "xformers"
]

for req in requirements:
    subprocess.run([sys.executable, "-m", "pip", "install", "-q", req], check=True)

print("✅ Requirements installed!")
```

### **Cell 2: Upload Files**
```python
# 📁 Upload your Python files
from google.colab import files

print("📁 Please upload these files:")
print("1. dataset_generator.py")
print("2. colab_tinyllama_finetune.py")
print("\nClick 'Choose Files' and select both files:")

uploaded = files.upload()

# Verify files
import os
required_files = ['dataset_generator.py', 'colab_tinyllama_finetune.py']
missing_files = [f for f in required_files if f not in os.listdir('.')]

if missing_files:
    print(f"❌ Missing files: {missing_files}")
    print("Please upload the missing files and run this cell again.")
else:
    print("✅ All required files uploaded!")
    print(f"📂 Available files: {list(uploaded.keys())}")
```

### **Cell 3: Generate Dataset**
```python
# 📊 Generate extensive training dataset
print("📊 Generating extensive dairy SQL training dataset...")

# Execute the dataset generator
exec(open('dataset_generator.py').read())

# Verify dataset was created
import os
if os.path.exists('extensive_dairy_sql_dataset.json'):
    import json
    with open('extensive_dairy_sql_dataset.json', 'r') as f:
        data = json.load(f)
    print(f"✅ Dataset created with {len(data)} samples!")
    
    # Show sample
    print("\n📋 Sample training example:")
    sample = data[0]
    print(f"Question: {sample['input']}")
    print(f"SQL: {sample['output']}")
    print(f"Category: {sample['category']}")
else:
    print("❌ Dataset not created. Check for errors above.")
```

### **Cell 4: Start Fine-tuning**
```python
# 🚀 Start TinyLlama fine-tuning
import time

print("🚀 Starting TinyLlama fine-tuning...")
print("⏱️ Expected time: 25-35 minutes")
print("📊 Progress will be shown below:")
print("-" * 50)

start_time = time.time()

# Execute the fine-tuning script
exec(open('colab_tinyllama_finetune.py').read())

end_time = time.time()
training_time = (end_time - start_time) / 60

print(f"\n🎉 Training completed in {training_time:.1f} minutes!")
print("📁 Model saved to: ./tinyllama-dairy-sql")
```

### **Cell 5: Test Your Model**
```python
# 🧪 Test the trained model
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

print("🧪 Testing your trained TinyLlama model...")

# Load your trained model
model_path = "./tinyllama-dairy-sql"
tokenizer = AutoTokenizer.from_pretrained(model_path)
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    device_map="auto",
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
)

# Test questions
test_questions = [
    "How many sites do I have?",
    "How many sites does Sunrise Dairy Farm have?",
    "What is the average milk production?",
    "Which sites have milk fat percentage above 3.5%?",
    "Show me all customers in Canada",
    "What visits were conducted last month?"
]

print("🔍 Testing with dairy-specific questions:")
print("=" * 60)

for i, question in enumerate(test_questions, 1):
    # Format prompt for TinyLlama
    prompt = f"""<|system|>
You are a helpful AI assistant that generates SQL queries for dairy farm database questions.

<|user|>
Generate SQL query for the following question about dairy farm data:

Question: {question}

<|assistant|>
"""
    
    # Generate response
    inputs = tokenizer(prompt, return_tensors="pt", truncation=True)
    if torch.cuda.is_available():
        inputs = {k: v.cuda() for k, v in inputs.items()}
    
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=100,
            temperature=0.7,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    
    # Decode response
    response = tokenizer.decode(outputs[0][inputs['input_ids'].shape[1]:], skip_special_tokens=True)
    
    print(f"\n{i}. ❓ Question: {question}")
    print(f"   🔍 Generated SQL: {response.strip()}")
    print("-" * 40)

print("\n✅ Testing completed! Your model is working!")
```

### **Cell 6: Download Your Model**
```python
# 💾 Download your trained model
import shutil
import os
from google.colab import files

print("💾 Preparing your trained model for download...")

# Check if model exists
if os.path.exists("./tinyllama-dairy-sql"):
    # Zip the model
    shutil.make_archive('tinyllama-dairy-sql-model', 'zip', './tinyllama-dairy-sql')
    
    # Get file size
    file_size = os.path.getsize('tinyllama-dairy-sql-model.zip') / (1024*1024)
    print(f"📦 Model zipped! Size: {file_size:.1f} MB")
    
    # Download
    print("⬇️ Starting download...")
    files.download('tinyllama-dairy-sql-model.zip')
    
    print("✅ Download started! Check your browser's download folder.")
    print("\n🎯 What you got:")
    print("   • Fine-tuned TinyLlama 1.1B model")
    print("   • Trained on 387 dairy-specific SQL examples")
    print("   • Ready for offline use")
    print("   • Understands your database schema")
    
else:
    print("❌ Model not found. Make sure training completed successfully.")

print("\n🚀 Next steps:")
print("1. Extract the zip file")
print("2. Use tinyllama_dairy_assistant.py to interact with your model")
print("3. Connect to your actual database for real queries!")
```

## ⏱️ **Expected Timeline**

| Step | Time | What Happens |
|------|------|--------------|
| Setup | 2-3 min | Install packages, check GPU |
| Upload | 1-2 min | Upload Python files |
| Dataset | 1-2 min | Generate 387 training samples |
| Training | 25-35 min | Fine-tune TinyLlama |
| Testing | 2-3 min | Validate trained model |
| Download | 1-2 min | Zip and download model |
| **Total** | **30-45 min** | Complete process |

## 📊 **What to Watch For**

### ✅ **Good Signs:**
```
🔥 GPU Available: True
🚀 GPU: Tesla T4/V100/A100
📊 Dataset created with 387 samples!
🎯 Trainable params: 4,194,304
{'loss': 2.1234, 'learning_rate': 0.0002, 'epoch': 0.5}
{'loss': 1.8765, 'learning_rate': 0.00015, 'epoch': 1.0}  # Loss decreasing
✅ Model saved to: ./tinyllama-dairy-sql
```

### ❌ **Warning Signs:**
```
⚠️ No GPU! Enable GPU in Runtime → Change runtime type
❌ CUDA out of memory  # Reduce batch size
❌ Missing files: ['dataset_generator.py']  # Upload files
```

## 🔧 **Troubleshooting**

### **No GPU Available:**
1. Runtime → Change runtime type → GPU
2. Wait for new runtime to start
3. Re-run setup cell

### **Out of Memory:**
```python
# In colab_tinyllama_finetune.py, reduce batch size:
per_device_train_batch_size=2,  # Reduce from 4
gradient_accumulation_steps=8,   # Increase to maintain quality
```

### **Files Not Found:**
- Make sure you uploaded both Python files
- Check file names match exactly
- Re-upload if needed

### **Training Stuck:**
- Check GPU is still enabled
- Look for error messages in output
- Restart runtime if needed

## 🎉 **Success! What You'll Have**

After completion, you'll have:
- ✅ **Fine-tuned TinyLlama model** (2-3 GB)
- ✅ **Trained on your dairy database schema**
- ✅ **387 comprehensive training examples**
- ✅ **Ready for offline use**
- ✅ **Understands dairy terminology**

## 🚀 **Using Your Model Locally**

After downloading:
```python
from transformers import AutoTokenizer, AutoModelForCausalLM

# Load your trained model
tokenizer = AutoTokenizer.from_pretrained("./tinyllama-dairy-sql")
model = AutoModelForCausalLM.from_pretrained("./tinyllama-dairy-sql")

# Ask questions
question = "How many sites do I have?"
prompt = f"""<|system|>
You are a helpful AI assistant that generates SQL queries for dairy farm database questions.

<|user|>
Generate SQL query for the following question about dairy farm data:

Question: {question}

<|assistant|>
"""

inputs = tokenizer(prompt, return_tensors="pt")
outputs = model.generate(**inputs, max_new_tokens=50, temperature=0.7)
response = tokenizer.decode(outputs[0][inputs['input_ids'].shape[1]:], skip_special_tokens=True)
print(f"SQL: {response}")
```

**Ready to start? Copy the cells above into Google Colab and run them one by one!** 🚀
