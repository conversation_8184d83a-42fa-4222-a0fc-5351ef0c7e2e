# 🚀 Kaggle Setup Guide for TinyLlama Dairy SQL Fine-tuning

## 📋 **Quick Start Checklist**

- [ ] Create Kaggle account
- [ ] Enable GPU in notebook settings
- [ ] Upload project files
- [ ] Install requirements
- [ ] Run fine-tuning script
- [ ] Download trained model

## 🔧 **Step-by-Step Setup**

### 1. **Create Kaggle Account & Notebook**

1. Go to [kaggle.com](https://www.kaggle.com) and create account
2. Click "Code" → "New Notebook"
3. **IMPORTANT**: Enable GPU in settings:
   - Click "Settings" (gear icon)
   - Under "Accelerator" select **"GPU P100"** or **"GPU T4 x2"**
   - Click "Save"

### 2. **Upload Project Files**

Upload these files to your Kaggle notebook:
```
📁 Input Files:
├── dataset_generator.py
├── kaggle_tinyllama_finetune.py
├── kaggle_requirements.txt
├── cargill.db (1).sql
└── KAGGLE_SETUP_GUIDE.md
```

**How to upload:**
- Click "Input" → "Upload" → "New Dataset"
- Upload all files as a dataset
- Name it "dairy-llm-finetune"

### 3. **Kaggle Notebook Code**

Copy this code into your Kaggle notebook:

```python
# Cell 1: Setup and Install Requirements
import os
import sys

# Add input directory to path
sys.path.append('/kaggle/input/dairy-llm-finetune')

# Install requirements
!pip install -q torch>=2.0.0 transformers>=4.35.0 datasets>=2.14.0 accelerate>=0.24.0 peft>=0.6.0 bitsandbytes>=0.41.0

print("✅ Requirements installed!")

# Cell 2: Check GPU and Memory
import torch
print(f"🔥 CUDA Available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"🚀 GPU: {torch.cuda.get_device_name(0)}")
    print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

# Cell 3: Generate Dataset
print("📊 Generating extensive training dataset...")
exec(open('/kaggle/input/dairy-llm-finetune/dataset_generator.py').read())

# Cell 4: Start Fine-tuning
print("🚀 Starting TinyLlama fine-tuning...")
exec(open('/kaggle/input/dairy-llm-finetune/kaggle_tinyllama_finetune.py').read())

# Cell 5: Download Model (Optional)
import shutil
import zipfile

# Zip the trained model
shutil.make_archive('/kaggle/working/tinyllama-dairy-sql-model', 'zip', '/kaggle/working/tinyllama-dairy-sql')
print("📦 Model zipped and ready for download!")
print("💾 Download from: /kaggle/working/tinyllama-dairy-sql-model.zip")
```

## ⚡ **Optimized Kaggle Code (Single Cell)**

For fastest setup, use this single cell:

```python
# 🐄 TinyLlama Dairy SQL Fine-tuning - One-Click Setup
import os, sys, subprocess, torch
sys.path.append('/kaggle/input/dairy-llm-finetune')

# Install requirements
subprocess.run([sys.executable, '-m', 'pip', 'install', '-q', 
               'torch>=2.0.0', 'transformers>=4.35.0', 'datasets>=2.14.0', 
               'accelerate>=0.24.0', 'peft>=0.6.0', 'bitsandbytes>=0.41.0'], 
               check=True)

print(f"🔥 GPU: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'None'}")

# Generate dataset and train
exec(open('/kaggle/input/dairy-llm-finetune/dataset_generator.py').read())
exec(open('/kaggle/input/dairy-llm-finetune/kaggle_tinyllama_finetune.py').read())

print("🎉 Training completed! Model saved to /kaggle/working/tinyllama-dairy-sql")
```

## 🎯 **Expected Performance**

### **Training Time:**
- **Dataset Generation**: 2-3 minutes
- **Model Loading**: 3-5 minutes  
- **Fine-tuning**: 15-25 minutes
- **Total Time**: ~30 minutes

### **Memory Usage:**
- **GPU Memory**: ~10-12 GB (with quantization)
- **RAM**: ~8-10 GB
- **Storage**: ~3-4 GB for model

### **Dataset Size:**
- **Training Samples**: 400-600 queries
- **Categories**: 8 different query types
- **Coverage**: All database tables and relationships

## 🔧 **Troubleshooting**

### **Out of Memory Error:**
```python
# Reduce batch size in kaggle_tinyllama_finetune.py
training_args = TrainingArguments(
    per_device_train_batch_size=2,  # Reduce from 4
    gradient_accumulation_steps=8,   # Increase to maintain effective batch size
)
```

### **Slow Training:**
```python
# Reduce max_steps for faster completion
training_args = TrainingArguments(
    max_steps=500,  # Reduce from 1000
    eval_steps=50,  # Reduce evaluation frequency
)
```

### **Import Errors:**
```python
# If modules not found, copy files to working directory
import shutil
shutil.copy('/kaggle/input/dairy-llm-finetune/dataset_generator.py', '/kaggle/working/')
shutil.copy('/kaggle/input/dairy-llm-finetune/kaggle_tinyllama_finetune.py', '/kaggle/working/')
```

## 📊 **Monitoring Training**

Watch for these indicators:

```
✅ Good Signs:
- GPU utilization > 80%
- Training loss decreasing
- No CUDA out of memory errors
- Eval loss improving

❌ Warning Signs:
- GPU utilization < 50%
- Loss not decreasing after 100 steps
- Frequent memory warnings
```

## 💾 **Downloading Your Model**

After training completes:

```python
# Method 1: Direct download (if small enough)
from IPython.display import FileLink
FileLink('/kaggle/working/tinyllama-dairy-sql-model.zip')

# Method 2: Save to Kaggle dataset
# Go to "Data" → "New Dataset" → Upload the model folder

# Method 3: Push to Hugging Face Hub (recommended)
from huggingface_hub import HfApi
api = HfApi()
api.upload_folder(
    folder_path="/kaggle/working/tinyllama-dairy-sql",
    repo_id="your-username/tinyllama-dairy-sql",
    repo_type="model"
)
```

## 🚀 **Using Your Trained Model**

After downloading, use it locally:

```python
from transformers import AutoTokenizer, AutoModelForCausalLM

tokenizer = AutoTokenizer.from_pretrained("./tinyllama-dairy-sql")
model = AutoModelForCausalLM.from_pretrained("./tinyllama-dairy-sql")

# Ask questions
question = "How many sites do I have?"
prompt = f"""<|system|>
You are a helpful AI assistant that generates SQL queries for dairy farm database questions.

<|user|>
Generate SQL query for the following question about dairy farm data:

Question: {question}

<|assistant|>
"""

inputs = tokenizer(prompt, return_tensors="pt")
outputs = model.generate(**inputs, max_new_tokens=50, temperature=0.7)
response = tokenizer.decode(outputs[0][inputs['input_ids'].shape[1]:], skip_special_tokens=True)
print(f"SQL: {response}")
```

## 🎯 **Success Metrics**

Your model should achieve:
- ✅ **Generate syntactically correct SQL** for 90%+ of queries
- ✅ **Handle dairy-specific terminology** accurately
- ✅ **Understand table relationships** and joins
- ✅ **Response time** under 2 seconds per query
- ✅ **Model size** around 2-3 GB (quantized)

## 📞 **Support**

If you encounter issues:

1. **Check GPU is enabled** in Kaggle settings
2. **Verify all files uploaded** correctly
3. **Monitor memory usage** during training
4. **Reduce batch size** if out of memory
5. **Check training logs** for error messages

## 🏆 **Pro Tips**

1. **Use GPU P100** for fastest training
2. **Enable internet** in Kaggle settings for model downloads
3. **Save checkpoints** frequently in case of interruption
4. **Monitor training loss** - should decrease steadily
5. **Test immediately** after training completes

**Ready to start? Upload your files to Kaggle and run the code!** 🚀
