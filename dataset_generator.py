#!/usr/bin/env python3
"""
Dataset Generator for Dairy Database Fine-tuning
Generates training data for text-to-SQL tasks based on Cargill dairy database schema
"""

import json
import random
from typing import List, Dict, Tuple

class DairyDatasetGenerator:
    def __init__(self):
        # Database schema information extracted from your SQL file
        self.tables = {
            'customer': {
                'columns': ['id', 'businessName', 'customerCode', 'accountType', 'country', 'street', 'state', 'city', 'postalCode', 'siteCount', 'dateOfLastVisit'],
                'description': 'Customer/account information including dairy farms and businesses'
            },
            'site': {
                'columns': ['id', 'accountId', 'siteName', 'currentMilkPrice', 'milkingSystemType', 'numberOfParlorStalls', 'lactatingAnimal', 'daysInMilk', 'milk', 'milkFatPercent', 'milkProteinPercent', 'penCount', 'dateOfLastVisit'],
                'description': 'Individual dairy farm sites with production metrics'
            },
            'visit': {
                'columns': ['id', 'customerId', 'siteId', 'visitDate', 'firstName', 'lastName', 'visitStatus', 'visitName', 'rumenHealth', 'locomotionScore', 'bodyCondition', 'animalAnalysis', 'heatStress'],
                'description': 'Farm visits and assessments by specialists'
            },
            'pen': {
                'columns': ['id', 'accountId', 'siteId', 'barnId', 'name', 'barnName', 'housingSystemType', 'feedingSystemType', 'numberOfStalls', 'milkingFrequency', 'animals', 'daysInMilk', 'milk', 'dietId'],
                'description': 'Animal housing pens within dairy farms'
            },
            'diets': {
                'columns': ['id', 'animalType', 'barnId', 'breedName', 'name', 'numberOfAnimals', 'siteId'],
                'description': 'Feed diets and nutrition programs for animals'
            },
            'animalClass': {
                'columns': ['id', 'className', 'en_class', 'en_subClass'],
                'description': 'Classification of animals (lactating, dry, heifer, calf, etc.)'
            },
            'countries': {
                'columns': ['id', 'name', 'countryCode'],
                'description': 'Country reference data'
            },
            'noteBook': {
                'columns': ['id', 'title', 'note', 'accountId', 'siteId', 'visitId', 'section', 'category', 'createdDate'],
                'description': 'Notes and observations from farm visits'
            }
        }
        
        # Common dairy-specific terms and phrases
        self.dairy_terms = [
            'lactating cows', 'dry cows', 'heifers', 'calves', 'milk production',
            'milk fat', 'milk protein', 'days in milk', 'parlor stalls',
            'milking frequency', 'rumen health', 'body condition', 'heat stress',
            'locomotion score', 'feed intake', 'diet', 'nutrition'
        ]
        
        # Sample business names for realistic queries
        self.sample_businesses = [
            'Sunrise Dairy Farm', 'Green Valley Dairy', 'Meadowbrook Farm',
            'Prairie View Dairy', 'Hillside Dairy Co', 'Golden Fields Farm'
        ]

    def generate_basic_count_queries(self) -> List[Dict]:
        """Generate basic counting queries"""
        queries = []
        
        # Site counting queries
        site_questions = [
            ("How many sites do I have?", "SELECT COUNT(*) FROM site;"),
            ("How many dairy farms are in the database?", "SELECT COUNT(*) FROM site;"),
            ("What's the total number of sites?", "SELECT COUNT(*) FROM site;"),
            ("How many farm locations do we have?", "SELECT COUNT(*) FROM site;"),
        ]
        
        # Customer counting queries
        customer_questions = [
            ("How many customers do we have?", "SELECT COUNT(*) FROM customer;"),
            ("What's the total number of accounts?", "SELECT COUNT(*) FROM customer;"),
            ("How many dairy businesses are registered?", "SELECT COUNT(*) FROM customer;"),
        ]
        
        # Visit counting queries
        visit_questions = [
            ("How many visits have been conducted?", "SELECT COUNT(*) FROM visit;"),
            ("What's the total number of farm visits?", "SELECT COUNT(*) FROM visit;"),
            ("How many site inspections do we have recorded?", "SELECT COUNT(*) FROM visit;"),
        ]
        
        all_questions = site_questions + customer_questions + visit_questions
        
        for question, sql in all_questions:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "basic_count"
            })
        
        return queries

    def generate_customer_specific_queries(self) -> List[Dict]:
        """Generate queries about specific customers"""
        queries = []
        
        # Customer site count queries
        customer_site_questions = [
            ("How many sites does customer 'Sunrise Dairy Farm' have?", 
             "SELECT siteCount FROM customer WHERE businessName = 'Sunrise Dairy Farm';"),
            ("What is the site count for account 'Green Valley Dairy'?",
             "SELECT siteCount FROM customer WHERE businessName = 'Green Valley Dairy';"),
            ("How many farm locations does 'Meadowbrook Farm' operate?",
             "SELECT siteCount FROM customer WHERE businessName = 'Meadowbrook Farm';"),
        ]
        
        # Customer information queries
        customer_info_questions = [
            ("What is the address of 'Prairie View Dairy'?",
             "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Prairie View Dairy';"),
            ("Where is 'Hillside Dairy Co' located?",
             "SELECT city, state, country FROM customer WHERE businessName = 'Hillside Dairy Co';"),
            ("What's the customer code for 'Golden Fields Farm'?",
             "SELECT customerCode FROM customer WHERE businessName = 'Golden Fields Farm';"),
        ]
        
        all_questions = customer_site_questions + customer_info_questions
        
        for question, sql in all_questions:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "customer_specific"
            })
        
        return queries

    def generate_visit_queries(self) -> List[Dict]:
        """Generate queries about farm visits"""
        queries = []
        
        visit_questions = [
            ("What data is inside visit 'Weekly Health Check'?",
             "SELECT * FROM visit WHERE visitName = 'Weekly Health Check';"),
            ("Show me all information for visit 'Nutrition Assessment'",
             "SELECT * FROM visit WHERE visitName = 'Nutrition Assessment';"),
            ("What visits were conducted at site ID 'site123'?",
             "SELECT visitName, visitDate, visitStatus FROM visit WHERE siteId = 'site123';"),
            ("Who conducted the visit named 'Herd Evaluation'?",
             "SELECT firstName, lastName FROM visit WHERE visitName = 'Herd Evaluation';"),
            ("What was the rumen health score for visit 'Health Inspection'?",
             "SELECT rumenHealth FROM visit WHERE visitName = 'Health Inspection';"),
        ]
        
        for question, sql in visit_questions:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "visit_data"
            })
        
        return queries

    def generate_production_queries(self) -> List[Dict]:
        """Generate queries about milk production and farm metrics"""
        queries = []
        
        production_questions = [
            ("What is the average milk production across all sites?",
             "SELECT AVG(milk) FROM site;"),
            ("Which sites have milk fat percentage above 3.5%?",
             "SELECT siteName, milkFatPercent FROM site WHERE milkFatPercent > 3.5;"),
            ("What's the total number of lactating animals across all farms?",
             "SELECT SUM(lactatingAnimal) FROM site;"),
            ("Show me sites with more than 100 parlor stalls",
             "SELECT siteName, numberOfParlorStalls FROM site WHERE numberOfParlorStalls > 100;"),
            ("What's the average days in milk for all sites?",
             "SELECT AVG(daysInMilk) FROM site;"),
        ]
        
        for question, sql in production_questions:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "production_metrics"
            })
        
        return queries

    def generate_complex_join_queries(self) -> List[Dict]:
        """Generate more complex queries with joins"""
        queries = []
        
        join_questions = [
            ("Show me all sites for customer 'Sunrise Dairy Farm'",
             "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Sunrise Dairy Farm';"),
            ("What visits were conducted at sites owned by 'Green Valley Dairy'?",
             "SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Green Valley Dairy';"),
            ("How many pens does each customer have in total?",
             "SELECT c.businessName, COUNT(p.id) as total_pens FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;"),
        ]
        
        for question, sql in join_questions:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "complex_joins"
            })
        
        return queries

    def generate_full_dataset(self) -> List[Dict]:
        """Generate the complete training dataset"""
        dataset = []
        
        # Generate different types of queries
        dataset.extend(self.generate_basic_count_queries())
        dataset.extend(self.generate_customer_specific_queries())
        dataset.extend(self.generate_visit_queries())
        dataset.extend(self.generate_production_queries())
        dataset.extend(self.generate_complex_join_queries())
        
        # Shuffle the dataset
        random.shuffle(dataset)
        
        return dataset

    def save_dataset(self, filename: str = "dairy_sql_dataset.json"):
        """Save the dataset to a JSON file"""
        dataset = self.generate_full_dataset()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, indent=2, ensure_ascii=False)
        
        print(f"Dataset saved to {filename}")
        print(f"Total samples: {len(dataset)}")
        
        # Print sample entries
        print("\nSample entries:")
        for i, sample in enumerate(dataset[:3]):
            print(f"\nSample {i+1}:")
            print(f"Question: {sample['input']}")
            print(f"SQL: {sample['output']}")

if __name__ == "__main__":
    generator = DairyDatasetGenerator()
    generator.save_dataset()
