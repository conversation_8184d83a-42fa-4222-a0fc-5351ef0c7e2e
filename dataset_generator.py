#!/usr/bin/env python3
"""
EXTENSIVE Dataset Generator for Dairy Database Fine-tuning
Generates comprehensive training data for TinyLlama text-to-SQL tasks
Covers ALL database schema cases, edge cases, and dairy-specific scenarios
"""

import json
import random
from typing import List, Dict, Tuple
from datetime import datetime, timedelta
import itertools

class ExtensiveDairyDatasetGenerator:
    def __init__(self):
        # Complete database schema from your SQL file
        self.tables = {
            'animalClass': {
                'columns': ['id', 'className', 'de_class', 'de_subClass', 'en_class', 'en_subClass', 'fr_class', 'fr_subClass'],
                'description': 'Animal classifications with multilingual support',
                'sample_data': ['lactatingFresh', 'lactatingMilking', 'dryFarOff', 'dryCloseUp', 'heifer', 'calf']
            },
            'cities': {
                'columns': ['id', 'name', 'countryCode', 'countryName', 'stateCode', 'stateName'],
                'description': 'City reference data with geographic information'
            },
            'contact': {
                'columns': ['id', 'sfdcId', 'fullName', 'email', 'phoneNumber'],
                'description': 'Contact information for customers and users'
            },
            'countries': {
                'columns': ['id', 'name', 'countryCode', 'countryBusinessIdMapping', 'countryKey'],
                'description': 'Country reference data with business mappings'
            },
            'customer': {
                'columns': ['id', 'businessName', 'customerCode', 'accountType', 'type', 'country', 'street', 'state', 'city', 'postalCode', 'segmentId', 'contacts', 'favourite', 'siteCount', 'dateOfLastVisit', 'updated_at', 'userRoles', 'users', 'accessIdentifier'],
                'description': 'Customer/account information including dairy farms and businesses',
                'sample_data': ['Sunrise Dairy Farm', 'Green Valley Dairy', 'Meadowbrook Farm', 'Prairie View Dairy']
            },
            'diets': {
                'columns': ['id', 'animalType', 'animalTypeId', 'barnId', 'breedId', 'breedName', 'isActive', 'isDeleted', 'name', 'numberOfAnimals', 'siteId', 'source', 'analyzeOptimization', 'formulateOptimization', 'updated_at'],
                'description': 'Feed diets and nutrition programs for animals'
            },
            'earTag': {
                'columns': ['id', 'earTagName', 'accountId', 'siteId', 'accessIdentifier'],
                'description': 'Animal ear tag identification system'
            },
            'media': {
                'columns': ['id', 'filename', 'mediaName', 'mediaType', 'mime', 'noteId', 'path', 'reportType', 'accountId', 'visitId', 'userId', 'deleted'],
                'description': 'Media files associated with visits and notes'
            },
            'noteBook': {
                'columns': ['id', 'title', 'note', 'accountId', 'siteId', 'visitId', 'section', 'sectionId', 'sectionTitle', 'category', 'createUser', 'lastModifyUser', 'deleted', 'mediaItems', 'favourite', 'createdDate', 'updatedDate', 'accessIdentifier'],
                'description': 'Notes and observations from farm visits'
            },
            'notifications': {
                'columns': ['id', 'visitId', 'noteId', 'title', 'description', 'type', 'isRead', 'userId', 'deleted', 'scheduleTime', 'updatedDate', 'createdDate'],
                'description': 'System notifications for users'
            },
            'pen': {
                'columns': ['id', 'accountId', 'siteId', 'barnId', 'source', 'name', 'barnName', 'housingSystemType', 'feedingSystemType', 'numberOfStalls', 'milkingFrequency', 'animals', 'daysInMilk', 'milk', 'dietId', 'animalClassId', 'dryMatterIntake', 'asFedIntake', 'rationCostPerAnimal', 'isDeleted', 'createUser', 'updatedDate', 'accessIdentifier'],
                'description': 'Animal housing pens within dairy farms with detailed metrics'
            },
            'prospect': {
                'columns': ['id', 'businessName', 'customerCode', 'accountType', 'type', 'country', 'street', 'state', 'city', 'postalCode', 'segmentId', 'contacts', 'favourite', 'siteCount', 'dateOfLastVisit'],
                'description': 'Prospective customers and leads'
            },
            'segment': {
                'columns': ['id', 'name', 'defaultValue'],
                'description': 'Customer segmentation data'
            },
            'silage': {
                'columns': ['id', 'silageName', 'accountId', 'siteId', 'updated', 'accessIdentifier'],
                'description': 'Silage feed storage information'
            },
            'site': {
                'columns': ['id', 'accountId', 'siteName', 'currentMilkPrice', 'milkingSystemType', 'numberOfParlorStalls', 'lactatingAnimal', 'daysInMilk', 'milk', 'milkFatPercent', 'milkProteinPercent', 'milkOtherSolidsPercent', 'milkSomaticCellCount', 'bacteriaCellCount', 'dryMatterIntake', 'asFedIntake', 'netEnergyOfLactationDairy', 'rationCost', 'hasReport', 'penCount', 'dateOfLastVisit', 'updated_at', 'accessIdentifier'],
                'description': 'Individual dairy farm sites with comprehensive production metrics'
            },
            'states': {
                'columns': ['id', 'name', 'stateCode', 'countryCode', 'stateKey'],
                'description': 'State/province reference data'
            },
            'users': {
                'columns': ['id', 'guid', 'accountType', 'authenticationPlatform', 'email', 'fullName', 'mobileNumber', 'password', 'principalName', 'countryId', 'userPreferences', 'created_at', 'updated_at', 'isLoggedIn'],
                'description': 'System users and authentication data'
            },
            'visit': {
                'columns': ['id', 'customerId', 'siteId', 'visitDate', 'firstName', 'lastName', 'visitStatus', 'selected', 'visitName', 'isVisitAutoPublished', 'rumenHealth', 'locomotionScore', 'rumenHealthManureScore', 'foragePennState', 'penTimeBudgetTool', 'rumenFill', 'bodyCondition', 'animalAnalysis', 'milkSoldEvaluation', 'metabolicIncidence', 'roboticMilkEvaluation', 'pileAndBunker', 'tmrParticleScore', 'manureScreener', 'forageAuditScorecard', 'heatStress', 'profitabilityAnalysis', 'calfHeiferScorecard', 'selectedCurrency', 'unitOfMeasure', 'selectedPointScale', 'visitPublishedDate', 'createUser', 'lastModifyUser', 'updated_at', 'mobileLastUpdatedTime', 'accessIdentifier', 'usedPens', 'deleted'],
                'description': 'Comprehensive farm visits and assessments by specialists'
            },
            'visitReport': {
                'columns': ['id', 'visitId', 'fileName', 'fileId', 'accessIdentifier', 'created_at', 'is_synced'],
                'description': 'Reports generated from farm visits'
            }
        }

        # Comprehensive dairy-specific terms and phrases
        self.dairy_terms = [
            'lactating cows', 'dry cows', 'heifers', 'calves', 'milk production',
            'milk fat', 'milk protein', 'days in milk', 'parlor stalls',
            'milking frequency', 'rumen health', 'body condition', 'heat stress',
            'locomotion score', 'feed intake', 'diet', 'nutrition', 'somatic cell count',
            'bacteria cell count', 'dry matter intake', 'as fed intake', 'ration cost',
            'net energy', 'forage quality', 'TMR particle score', 'manure score',
            'housing system', 'feeding system', 'milking system', 'barn management'
        ]

        # Sample business names for realistic queries
        self.sample_businesses = [
            'Sunrise Dairy Farm', 'Green Valley Dairy', 'Meadowbrook Farm',
            'Prairie View Dairy', 'Hillside Dairy Co', 'Golden Fields Farm',
            'Maple Ridge Dairy', 'Valley View Farm', 'Heritage Dairy',
            'Cloverfield Farm', 'Riverside Dairy', 'Mountain View Ranch'
        ]

        # Sample visit names
        self.sample_visits = [
            'Weekly Health Check', 'Nutrition Assessment', 'Herd Evaluation',
            'Health Inspection', 'Production Review', 'Feed Analysis',
            'Reproductive Check', 'Facility Audit', 'Quality Assessment',
            'Welfare Inspection', 'Performance Review', 'System Evaluation'
        ]

        # Sample site names
        self.sample_sites = [
            'Main Farm', 'North Facility', 'South Barn', 'East Complex',
            'West Ranch', 'Central Dairy', 'Milking Parlor A', 'Facility B',
            'Production Unit 1', 'Dairy Complex 2', 'Farm Site Alpha', 'Beta Facility'
        ]

        # Countries from your database
        self.countries = [
            'United States', 'Canada', 'Brazil', 'Netherlands', 'Germany',
            'France', 'United Kingdom', 'Australia', 'New Zealand', 'Mexico'
        ]

        # States/Provinces
        self.states = [
            'California', 'Wisconsin', 'New York', 'Pennsylvania', 'Idaho',
            'Texas', 'Michigan', 'Minnesota', 'Iowa', 'Washington',
            'Ontario', 'Quebec', 'Alberta', 'British Columbia'
        ]

    def generate_basic_count_queries(self) -> List[Dict]:
        """Generate comprehensive basic counting queries for all tables"""
        queries = []

        # Site counting queries
        site_questions = [
            ("How many sites do I have?", "SELECT COUNT(*) FROM site;"),
            ("How many dairy farms are in the database?", "SELECT COUNT(*) FROM site;"),
            ("What's the total number of sites?", "SELECT COUNT(*) FROM site;"),
            ("How many farm locations do we have?", "SELECT COUNT(*) FROM site;"),
            ("Count all dairy farm sites", "SELECT COUNT(*) FROM site;"),
            ("Total number of farm facilities", "SELECT COUNT(*) FROM site;"),
        ]

        # Customer counting queries
        customer_questions = [
            ("How many customers do we have?", "SELECT COUNT(*) FROM customer;"),
            ("What's the total number of accounts?", "SELECT COUNT(*) FROM customer;"),
            ("How many dairy businesses are registered?", "SELECT COUNT(*) FROM customer;"),
            ("Count all customers", "SELECT COUNT(*) FROM customer;"),
            ("Total customer accounts", "SELECT COUNT(*) FROM customer;"),
            ("How many client accounts exist?", "SELECT COUNT(*) FROM customer;"),
        ]

        # Visit counting queries
        visit_questions = [
            ("How many visits have been conducted?", "SELECT COUNT(*) FROM visit;"),
            ("What's the total number of farm visits?", "SELECT COUNT(*) FROM visit;"),
            ("How many site inspections do we have recorded?", "SELECT COUNT(*) FROM visit;"),
            ("Count all farm visits", "SELECT COUNT(*) FROM visit;"),
            ("Total number of site visits", "SELECT COUNT(*) FROM visit;"),
            ("How many assessments were performed?", "SELECT COUNT(*) FROM visit;"),
        ]

        # Pen counting queries
        pen_questions = [
            ("How many pens are there in total?", "SELECT COUNT(*) FROM pen;"),
            ("What's the total number of animal pens?", "SELECT COUNT(*) FROM pen;"),
            ("Count all housing pens", "SELECT COUNT(*) FROM pen;"),
            ("How many animal enclosures do we have?", "SELECT COUNT(*) FROM pen;"),
        ]

        # Diet counting queries
        diet_questions = [
            ("How many diets are configured?", "SELECT COUNT(*) FROM diets;"),
            ("What's the total number of feeding programs?", "SELECT COUNT(*) FROM diets;"),
            ("Count all nutrition plans", "SELECT COUNT(*) FROM diets;"),
            ("How many feed formulations exist?", "SELECT COUNT(*) FROM diets;"),
        ]

        # Notes counting queries
        note_questions = [
            ("How many notes have been recorded?", "SELECT COUNT(*) FROM noteBook;"),
            ("What's the total number of observations?", "SELECT COUNT(*) FROM noteBook;"),
            ("Count all farm notes", "SELECT COUNT(*) FROM noteBook;"),
            ("How many documentation entries exist?", "SELECT COUNT(*) FROM noteBook;"),
        ]

        # Animal class counting queries
        animal_questions = [
            ("How many animal classifications are there?", "SELECT COUNT(*) FROM animalClass;"),
            ("What's the total number of animal categories?", "SELECT COUNT(*) FROM animalClass;"),
            ("Count all animal types", "SELECT COUNT(*) FROM animalClass;"),
        ]

        all_questions = (site_questions + customer_questions + visit_questions +
                        pen_questions + diet_questions + note_questions + animal_questions)

        for question, sql in all_questions:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "basic_count"
            })

        return queries

    def generate_customer_specific_queries(self) -> List[Dict]:
        """Generate comprehensive queries about specific customers"""
        queries = []

        # Customer site count queries
        for business in self.sample_businesses:
            customer_site_questions = [
                (f"How many sites does customer '{business}' have?",
                 f"SELECT siteCount FROM customer WHERE businessName = '{business}';"),
                (f"What is the site count for account '{business}'?",
                 f"SELECT siteCount FROM customer WHERE businessName = '{business}';"),
                (f"How many farm locations does '{business}' operate?",
                 f"SELECT siteCount FROM customer WHERE businessName = '{business}';"),
                (f"Show me the number of facilities for '{business}'",
                 f"SELECT siteCount FROM customer WHERE businessName = '{business}';"),
            ]

            # Customer information queries
            customer_info_questions = [
                (f"What is the address of '{business}'?",
                 f"SELECT street, city, state, postalCode FROM customer WHERE businessName = '{business}';"),
                (f"Where is '{business}' located?",
                 f"SELECT city, state, country FROM customer WHERE businessName = '{business}';"),
                (f"What's the customer code for '{business}'?",
                 f"SELECT customerCode FROM customer WHERE businessName = '{business}';"),
                (f"Show me the contact information for '{business}'",
                 f"SELECT contacts FROM customer WHERE businessName = '{business}';"),
                (f"What is the account type of '{business}'?",
                 f"SELECT accountType FROM customer WHERE businessName = '{business}';"),
                (f"When was '{business}' last visited?",
                 f"SELECT dateOfLastVisit FROM customer WHERE businessName = '{business}';"),
                (f"Is '{business}' marked as favourite?",
                 f"SELECT favourite FROM customer WHERE businessName = '{business}';"),
                (f"Show me all details for customer '{business}'",
                 f"SELECT * FROM customer WHERE businessName = '{business}';"),
            ]

            queries.extend([{
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "customer_specific"
            } for question, sql in customer_site_questions + customer_info_questions])

        # Customer queries by other attributes
        additional_queries = [
            ("Show me all customers in the United States",
             "SELECT businessName, city, state FROM customer WHERE country = 'United States';"),
            ("List all customers in Canada",
             "SELECT businessName, city, state FROM customer WHERE country = 'Canada';"),
            ("Find customers with more than 5 sites",
             "SELECT businessName, siteCount FROM customer WHERE siteCount > 5;"),
            ("Show customers with no sites",
             "SELECT businessName FROM customer WHERE siteCount = 0;"),
            ("List all favourite customers",
             "SELECT businessName FROM customer WHERE favourite = 1;"),
            ("Find customers by postal code 12345",
             "SELECT businessName, street, city FROM customer WHERE postalCode = '12345';"),
            ("Show customers in California",
             "SELECT businessName, city FROM customer WHERE state = 'California';"),
            ("List customers with account type 1",
             "SELECT businessName FROM customer WHERE accountType = 1;"),
        ]

        for question, sql in additional_queries:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "customer_specific"
            })

        return queries

    def generate_visit_queries(self) -> List[Dict]:
        """Generate comprehensive queries about farm visits"""
        queries = []

        # Specific visit queries
        for visit_name in self.sample_visits:
            visit_specific_questions = [
                (f"What data is inside visit '{visit_name}'?",
                 f"SELECT * FROM visit WHERE visitName = '{visit_name}';"),
                (f"Show me all information for visit '{visit_name}'",
                 f"SELECT * FROM visit WHERE visitName = '{visit_name}';"),
                (f"Who conducted the visit named '{visit_name}'?",
                 f"SELECT firstName, lastName FROM visit WHERE visitName = '{visit_name}';"),
                (f"What was the rumen health score for visit '{visit_name}'?",
                 f"SELECT rumenHealth FROM visit WHERE visitName = '{visit_name}';"),
                (f"Show the locomotion score for '{visit_name}'",
                 f"SELECT locomotionScore FROM visit WHERE visitName = '{visit_name}';"),
                (f"What was the body condition score in '{visit_name}'?",
                 f"SELECT bodyCondition FROM visit WHERE visitName = '{visit_name}';"),
                (f"When was visit '{visit_name}' conducted?",
                 f"SELECT visitDate FROM visit WHERE visitName = '{visit_name}';"),
                (f"What is the status of visit '{visit_name}'?",
                 f"SELECT visitStatus FROM visit WHERE visitName = '{visit_name}';"),
            ]

            queries.extend([{
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "visit_data"
            } for question, sql in visit_specific_questions])

        # General visit queries
        general_visit_questions = [
            ("Show all visits from the last month",
             "SELECT visitName, visitDate, firstName, lastName FROM visit WHERE visitDate >= date('now', '-1 month');"),
            ("List all completed visits",
             "SELECT visitName, visitDate FROM visit WHERE visitStatus = 'completed';"),
            ("Find visits with high rumen health scores",
             "SELECT visitName, rumenHealth FROM visit WHERE rumenHealth > 3;"),
            ("Show visits with poor locomotion scores",
             "SELECT visitName, locomotionScore FROM visit WHERE locomotionScore < 2;"),
            ("List all visits by John Smith",
             "SELECT visitName, visitDate FROM visit WHERE firstName = 'John' AND lastName = 'Smith';"),
            ("Find visits with heat stress issues",
             "SELECT visitName, heatStress FROM visit WHERE heatStress IS NOT NULL;"),
            ("Show all published visits",
             "SELECT visitName, visitPublishedDate FROM visit WHERE isVisitAutoPublished = 1;"),
            ("List visits that need sync",
             "SELECT visitName FROM visit WHERE needsSync = 1;"),
            ("Find visits with profitability analysis",
             "SELECT visitName, profitabilityAnalysis FROM visit WHERE profitabilityAnalysis IS NOT NULL;"),
            ("Show visits with calf heifer scorecard",
             "SELECT visitName, calfHeiferScorecard FROM visit WHERE calfHeiferScorecard IS NOT NULL;"),
        ]

        for question, sql in general_visit_questions:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "visit_data"
            })

        return queries

    def generate_production_queries(self) -> List[Dict]:
        """Generate comprehensive queries about milk production and farm metrics"""
        queries = []

        # Basic production metrics
        basic_production_questions = [
            ("What is the average milk production across all sites?",
             "SELECT AVG(milk) FROM site;"),
            ("What's the total milk production from all sites?",
             "SELECT SUM(milk) FROM site;"),
            ("What's the maximum milk production recorded?",
             "SELECT MAX(milk) FROM site;"),
            ("What's the minimum milk production recorded?",
             "SELECT MIN(milk) FROM site;"),
            ("Show the milk production range",
             "SELECT MIN(milk) as min_production, MAX(milk) as max_production FROM site;"),
        ]

        # Milk quality metrics
        milk_quality_questions = [
            ("Which sites have milk fat percentage above 3.5%?",
             "SELECT siteName, milkFatPercent FROM site WHERE milkFatPercent > 3.5;"),
            ("Show sites with milk protein above 3.2%",
             "SELECT siteName, milkProteinPercent FROM site WHERE milkProteinPercent > 3.2;"),
            ("What's the average milk fat percentage?",
             "SELECT AVG(milkFatPercent) FROM site;"),
            ("What's the average milk protein percentage?",
             "SELECT AVG(milkProteinPercent) FROM site;"),
            ("Find sites with high somatic cell count",
             "SELECT siteName, milkSomaticCellCount FROM site WHERE milkSomaticCellCount > 200000;"),
            ("Show sites with low bacteria cell count",
             "SELECT siteName, bacteriaCellCount FROM site WHERE bacteriaCellCount < 10000;"),
            ("List sites with milk other solids above 5.7%",
             "SELECT siteName, milkOtherSolidsPercent FROM site WHERE milkOtherSolidsPercent > 5.7;"),
        ]

        # Animal metrics
        animal_metrics_questions = [
            ("What's the total number of lactating animals across all farms?",
             "SELECT SUM(lactatingAnimal) FROM site;"),
            ("What's the average number of lactating animals per site?",
             "SELECT AVG(lactatingAnimal) FROM site;"),
            ("Which sites have more than 500 lactating animals?",
             "SELECT siteName, lactatingAnimal FROM site WHERE lactatingAnimal > 500;"),
            ("What's the average days in milk for all sites?",
             "SELECT AVG(daysInMilk) FROM site;"),
            ("Show sites with days in milk above 200",
             "SELECT siteName, daysInMilk FROM site WHERE daysInMilk > 200;"),
        ]

        # Facility metrics
        facility_questions = [
            ("Show me sites with more than 100 parlor stalls",
             "SELECT siteName, numberOfParlorStalls FROM site WHERE numberOfParlorStalls > 100;"),
            ("What's the average number of parlor stalls?",
             "SELECT AVG(numberOfParlorStalls) FROM site;"),
            ("Which sites have the most pens?",
             "SELECT siteName, penCount FROM site ORDER BY penCount DESC LIMIT 10;"),
            ("Show sites with more than 20 pens",
             "SELECT siteName, penCount FROM site WHERE penCount > 20;"),
        ]

        # Feed and nutrition metrics
        nutrition_questions = [
            ("What's the average dry matter intake across sites?",
             "SELECT AVG(dryMatterIntake) FROM site;"),
            ("Show sites with high as-fed intake",
             "SELECT siteName, asFedIntake FROM site WHERE asFedIntake > 50;"),
            ("What's the average ration cost per site?",
             "SELECT AVG(rationCost) FROM site;"),
            ("Find sites with ration cost above $5",
             "SELECT siteName, rationCost FROM site WHERE rationCost > 5.0;"),
            ("Show the net energy levels across sites",
             "SELECT siteName, netEnergyOfLactationDairy FROM site WHERE netEnergyOfLactationDairy IS NOT NULL;"),
        ]

        # Economic metrics
        economic_questions = [
            ("What's the current milk price range?",
             "SELECT MIN(currentMilkPrice) as min_price, MAX(currentMilkPrice) as max_price FROM site;"),
            ("Show sites with milk price above $20",
             "SELECT siteName, currentMilkPrice FROM site WHERE currentMilkPrice > 20;"),
            ("What's the average milk price?",
             "SELECT AVG(currentMilkPrice) FROM site;"),
        ]

        all_questions = (basic_production_questions + milk_quality_questions +
                        animal_metrics_questions + facility_questions +
                        nutrition_questions + economic_questions)

        for question, sql in all_questions:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "production_metrics"
            })

        return queries

    def generate_complex_join_queries(self) -> List[Dict]:
        """Generate comprehensive complex queries with joins"""
        queries = []

        # Customer-Site joins
        customer_site_joins = []
        for business in self.sample_businesses[:6]:  # Use first 6 to keep reasonable size
            customer_site_joins.extend([
                (f"Show me all sites for customer '{business}'",
                 f"SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{business}';"),
                (f"What are the production metrics for '{business}' sites?",
                 f"SELECT s.siteName, s.milk, s.milkFatPercent, s.milkProteinPercent FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{business}';"),
                (f"Show facility details for '{business}'",
                 f"SELECT s.siteName, s.numberOfParlorStalls, s.penCount FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{business}';"),
            ])

        # Visit-Site-Customer joins
        visit_joins = []
        for business in self.sample_businesses[:4]:
            visit_joins.extend([
                (f"What visits were conducted at sites owned by '{business}'?",
                 f"SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{business}';"),
                (f"Show visit assessments for '{business}' farms",
                 f"SELECT v.visitName, v.rumenHealth, v.locomotionScore, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = '{business}';"),
            ])

        # Pen aggregation queries
        pen_aggregation_queries = [
            ("How many pens does each customer have in total?",
             "SELECT c.businessName, COUNT(p.id) as total_pens FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;"),
            ("What's the total number of animals per customer?",
             "SELECT c.businessName, SUM(p.animals) as total_animals FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;"),
            ("Show average milk production per pen by customer",
             "SELECT c.businessName, AVG(p.milk) as avg_pen_milk FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;"),
            ("Count pens by housing system type",
             "SELECT housingSystemType, COUNT(*) as pen_count FROM pen GROUP BY housingSystemType;"),
            ("Show feeding system distribution",
             "SELECT feedingSystemType, COUNT(*) as pen_count FROM pen GROUP BY feedingSystemType;"),
        ]

        # Diet-related joins
        diet_joins = [
            ("Show diets used at each site",
             "SELECT s.siteName, d.name as diet_name, d.numberOfAnimals FROM site s JOIN diets d ON s.id = d.siteId;"),
            ("Which customers use the most diet programs?",
             "SELECT c.businessName, COUNT(d.id) as diet_count FROM customer c JOIN site s ON c.id = s.accountId JOIN diets d ON s.id = d.siteId GROUP BY c.businessName ORDER BY diet_count DESC;"),
            ("Show active diets by animal type",
             "SELECT animalType, COUNT(*) as diet_count FROM diets WHERE isActive = 1 GROUP BY animalType;"),
        ]

        # Note-related joins
        note_joins = [
            ("Show notes for each customer",
             "SELECT c.businessName, n.title, n.note FROM customer c JOIN noteBook n ON c.id = n.accountId;"),
            ("Count notes per site",
             "SELECT s.siteName, COUNT(n.id) as note_count FROM site s LEFT JOIN noteBook n ON s.id = n.siteId GROUP BY s.siteName;"),
            ("Show favourite notes by customer",
             "SELECT c.businessName, n.title FROM customer c JOIN noteBook n ON c.id = n.accountId WHERE n.favourite = 1;"),
        ]

        # Geographic analysis
        geographic_queries = [
            ("Show customer distribution by country",
             "SELECT country, COUNT(*) as customer_count FROM customer GROUP BY country;"),
            ("List sites by state with production totals",
             "SELECT c.state, COUNT(s.id) as site_count, SUM(s.milk) as total_production FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.state;"),
            ("Show average milk production by country",
             "SELECT c.country, AVG(s.milk) as avg_production FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.country;"),
        ]

        # Time-based analysis
        time_based_queries = [
            ("Show recent visits with site information",
             "SELECT v.visitName, v.visitDate, s.siteName, c.businessName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE v.visitDate >= date('now', '-30 days');"),
            ("Count visits per month by customer",
             "SELECT c.businessName, strftime('%Y-%m', v.visitDate) as month, COUNT(v.id) as visit_count FROM customer c JOIN site s ON c.id = s.accountId JOIN visit v ON s.id = v.siteId GROUP BY c.businessName, month;"),
        ]

        all_join_questions = (customer_site_joins + visit_joins + pen_aggregation_queries +
                             diet_joins + note_joins + geographic_queries + time_based_queries)

        for question, sql in all_join_questions:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "complex_joins"
            })

        return queries

    def generate_pen_queries(self) -> List[Dict]:
        """Generate comprehensive pen-related queries"""
        queries = []

        pen_questions = [
            ("Show all pens with more than 100 animals",
             "SELECT name, animals FROM pen WHERE animals > 100;"),
            ("What's the average number of animals per pen?",
             "SELECT AVG(animals) FROM pen;"),
            ("List pens by housing system type",
             "SELECT housingSystemType, COUNT(*) as pen_count FROM pen GROUP BY housingSystemType;"),
            ("Show pens with high milk production",
             "SELECT name, milk FROM pen WHERE milk > 1000;"),
            ("What's the average days in milk for pens?",
             "SELECT AVG(daysInMilk) FROM pen;"),
            ("Find pens with specific feeding systems",
             "SELECT name, feedingSystemType FROM pen WHERE feedingSystemType = 'TMR';"),
            ("Show pen utilization by barn",
             "SELECT barnName, COUNT(*) as pen_count FROM pen GROUP BY barnName;"),
            ("List pens with high milking frequency",
             "SELECT name, milkingFrequency FROM pen WHERE milkingFrequency > 2;"),
            ("Show pens with high dry matter intake",
             "SELECT name, dryMatterIntake FROM pen WHERE dryMatterIntake > 25;"),
            ("Find pens with low ration cost",
             "SELECT name, rationCostPerAnimal FROM pen WHERE rationCostPerAnimal < 3.0;"),
        ]

        for question, sql in pen_questions:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "pen_queries"
            })

        return queries

    def generate_animal_class_queries(self) -> List[Dict]:
        """Generate animal classification queries"""
        queries = []

        animal_class_questions = [
            ("Show all animal classifications",
             "SELECT className, en_class, en_subClass FROM animalClass;"),
            ("List lactating animal categories",
             "SELECT className, en_class FROM animalClass WHERE en_class = 'Lactating';"),
            ("Show dry cow classifications",
             "SELECT className, en_class FROM animalClass WHERE en_class = 'Dry';"),
            ("Find heifer classifications",
             "SELECT className, en_class FROM animalClass WHERE en_class = 'Heifer';"),
            ("Show all animal types in English",
             "SELECT en_class, en_subClass FROM animalClass;"),
            ("List animal classes in French",
             "SELECT fr_class, fr_subClass FROM animalClass;"),
            ("Show German animal classifications",
             "SELECT de_class, de_subClass FROM animalClass;"),
        ]

        for question, sql in animal_class_questions:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "animal_class"
            })

        return queries

    def generate_advanced_analytics_queries(self) -> List[Dict]:
        """Generate advanced analytics and reporting queries"""
        queries = []

        analytics_questions = [
            ("Show top 10 performing sites by milk production",
             "SELECT siteName, milk FROM site ORDER BY milk DESC LIMIT 10;"),
            ("Find sites with declining performance",
             "SELECT siteName, milk, milkFatPercent FROM site WHERE milk < (SELECT AVG(milk) FROM site);"),
            ("Show correlation between parlor stalls and production",
             "SELECT numberOfParlorStalls, AVG(milk) as avg_production FROM site GROUP BY numberOfParlorStalls;"),
            ("Identify high-maintenance sites",
             "SELECT s.siteName, COUNT(v.id) as visit_count FROM site s LEFT JOIN visit v ON s.id = v.siteId GROUP BY s.siteName HAVING visit_count > 5;"),
            ("Show seasonal visit patterns",
             "SELECT strftime('%m', visitDate) as month, COUNT(*) as visit_count FROM visit GROUP BY month;"),
            ("Find customers with multiple site types",
             "SELECT c.businessName, COUNT(DISTINCT s.milkingSystemType) as system_types FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.businessName HAVING system_types > 1;"),
            ("Show efficiency metrics by region",
             "SELECT c.state, AVG(s.milk/s.lactatingAnimal) as efficiency FROM customer c JOIN site s ON c.id = s.accountId GROUP BY c.state;"),
            ("Identify underperforming pens",
             "SELECT name, milk/animals as efficiency FROM pen WHERE animals > 0 ORDER BY efficiency ASC LIMIT 10;"),
        ]

        for question, sql in analytics_questions:
            queries.append({
                "instruction": "Generate SQL query for the following question about dairy farm data:",
                "input": question,
                "output": sql,
                "category": "advanced_analytics"
            })

        return queries

    def generate_full_dataset(self) -> List[Dict]:
        """Generate the complete comprehensive training dataset"""
        dataset = []

        print("Generating comprehensive dairy database training dataset...")

        # Generate all types of queries
        dataset.extend(self.generate_basic_count_queries())
        print(f"Added basic count queries: {len([q for q in dataset if q['category'] == 'basic_count'])}")

        dataset.extend(self.generate_customer_specific_queries())
        print(f"Added customer-specific queries: {len([q for q in dataset if q['category'] == 'customer_specific'])}")

        dataset.extend(self.generate_visit_queries())
        print(f"Added visit queries: {len([q for q in dataset if q['category'] == 'visit_data'])}")

        dataset.extend(self.generate_production_queries())
        print(f"Added production queries: {len([q for q in dataset if q['category'] == 'production_metrics'])}")

        dataset.extend(self.generate_complex_join_queries())
        print(f"Added complex join queries: {len([q for q in dataset if q['category'] == 'complex_joins'])}")

        dataset.extend(self.generate_pen_queries())
        print(f"Added pen queries: {len([q for q in dataset if q['category'] == 'pen_queries'])}")

        dataset.extend(self.generate_animal_class_queries())
        print(f"Added animal class queries: {len([q for q in dataset if q['category'] == 'animal_class'])}")

        dataset.extend(self.generate_advanced_analytics_queries())
        print(f"Added advanced analytics queries: {len([q for q in dataset if q['category'] == 'advanced_analytics'])}")

        # Shuffle the dataset
        random.shuffle(dataset)

        print(f"Total dataset size: {len(dataset)} samples")
        return dataset

    def save_dataset(self, filename: str = "dairy_sql_dataset.json"):
        """Save the dataset to a JSON file"""
        dataset = self.generate_full_dataset()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, indent=2, ensure_ascii=False)
        
        print(f"Dataset saved to {filename}")
        print(f"Total samples: {len(dataset)}")
        
        # Print sample entries
        print("\nSample entries:")
        for i, sample in enumerate(dataset[:3]):
            print(f"\nSample {i+1}:")
            print(f"Question: {sample['input']}")
            print(f"SQL: {sample['output']}")

if __name__ == "__main__":
    generator = ExtensiveDairyDatasetGenerator()
    generator.save_dataset("extensive_dairy_sql_dataset.json")
