[{"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of farm visits?", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Where is 'Hillside Dairy Co' located?", "output": "SELECT city, state, country FROM customer WHERE businessName = 'Hillside Dairy Co';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations do we have?", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many dairy businesses are registered?", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the address of 'Prairie View Dairy'?", "output": "SELECT street, city, state, postalCode FROM customer WHERE businessName = 'Prairie View Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Which sites have milk fat percentage above 3.5%?", "output": "SELECT siteName, milkFatPercent FROM site WHERE milkFatPercent > 3.5;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many dairy farms are in the database?", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What visits were conducted at sites owned by 'Green Valley Dairy'?", "output": "SELECT v.visitName, v.visitDate, s.siteName FROM visit v JOIN site s ON v.siteId = s.id JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Green Valley Dairy';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many visits have been conducted?", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all sites for customer 'Sunrise Dairy Farm'", "output": "SELECT s.siteName, s.milk, s.lactatingAnimal FROM site s JOIN customer c ON s.accountId = c.id WHERE c.businessName = 'Sunrise Dairy Farm';", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the site count for account 'Green Valley Dairy'?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Green Valley Dairy';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many farm locations does 'Meadowbrook Farm' operate?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Meadowbrook Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of sites?", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the customer code for 'Golden Fields Farm'?", "output": "SELECT customerCode FROM customer WHERE businessName = 'Golden Fields Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many pens does each customer have in total?", "output": "SELECT c.businessName, COUNT(p.id) as total_pens FROM customer c JOIN site s ON c.id = s.accountId JOIN pen p ON s.id = p.siteId GROUP BY c.businessName;", "category": "complex_joins"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me sites with more than 100 parlor stalls", "output": "SELECT siteName, numberOfParlorStalls FROM site WHERE numberOfParlorStalls > 100;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What is the average milk production across all sites?", "output": "SELECT AVG(milk) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What data is inside visit 'Weekly Health Check'?", "output": "SELECT * FROM visit WHERE visitName = 'Weekly Health Check';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What was the rumen health score for visit 'Health Inspection'?", "output": "SELECT rumenHealth FROM visit WHERE visitName = 'Health Inspection';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Who conducted the visit named 'Herd <PERSON>'?", "output": "SELECT firstName, lastName FROM visit WHERE visitName = 'Herd Evaluation';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of lactating animals across all farms?", "output": "SELECT SUM(lactatingAnimal) FROM site;", "category": "production_metrics"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many customers do we have?", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What visits were conducted at site ID 'site123'?", "output": "SELECT visitName, visitDate, visitStatus FROM visit WHERE siteId = 'site123';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many site inspections do we have recorded?", "output": "SELECT COUNT(*) FROM visit;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites do I have?", "output": "SELECT COUNT(*) FROM site;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "Show me all information for visit 'Nutrition Assessment'", "output": "SELECT * FROM visit WHERE visitName = 'Nutrition Assessment';", "category": "visit_data"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the total number of accounts?", "output": "SELECT COUNT(*) FROM customer;", "category": "basic_count"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "How many sites does customer 'Sunrise Dairy Farm' have?", "output": "SELECT siteCount FROM customer WHERE businessName = 'Sunrise Dairy Farm';", "category": "customer_specific"}, {"instruction": "Generate SQL query for the following question about dairy farm data:", "input": "What's the average days in milk for all sites?", "output": "SELECT AVG(daysInMilk) FROM site;", "category": "production_metrics"}]