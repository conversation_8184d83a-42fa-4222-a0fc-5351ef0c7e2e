# Free Platforms for Training Your Dairy LLM

## 🚀 **Recommended Platforms**

### 1. **Google Colab** (BEST OPTION)
- **Free GPU**: Tesla T4 (16GB VRAM) for up to 12 hours
- **Pro Version**: $10/month for better GPUs (A100, V100)
- **Perfect for**: CodeT5+ 220M/770M models
- **Setup**: Just upload your files and run!

**Colab Setup Steps:**
```python
# Install requirements
!pip install -r requirements.txt

# Run training
!python fine_tune_dairy_llm.py
```

### 2. **Kaggle Notebooks**
- **Free GPU**: Tesla P100 (16GB) for 30 hours/week
- **Free TPU**: Also available
- **Storage**: 20GB persistent storage
- **Perfect for**: All recommended models

**Kaggle Setup:**
1. Create account at kaggle.com
2. Go to "Code" → "New Notebook"
3. Enable GPU in settings
4. Upload your files

### 3. **Paperspace Gradient**
- **Free Tier**: Limited GPU hours
- **Good for**: Testing and small experiments
- **Paid Tiers**: Very affordable ($0.45/hour)

### 4. **Hugging Face Spaces**
- **Free**: CPU-only training (slow)
- **Paid**: GPU access available
- **Good for**: Model hosting after training

## 💡 **Training Strategy**

### For CodeT5+ 220M (Recommended):
- **Memory needed**: ~4GB VRAM
- **Training time**: 1-2 hours on free GPU
- **Platform**: Google Colab Free is perfect

### For CodeT5+ 770M:
- **Memory needed**: ~8GB VRAM  
- **Training time**: 2-4 hours
- **Platform**: Google Colab or Kaggle

### For larger models (7B+):
- **Memory needed**: 16GB+ VRAM
- **Platform**: Google Colab Pro or Kaggle
- **Consider**: Model quantization

## 🔧 **Optimization Tips**

### Memory Optimization:
```python
# Add to your training script
training_args = TrainingArguments(
    per_device_train_batch_size=2,  # Reduce if OOM
    gradient_accumulation_steps=4,   # Maintain effective batch size
    fp16=True,                      # Use mixed precision
    dataloader_pin_memory=False,    # Reduce memory usage
)
```

### For Limited GPU Time:
1. **Start with smaller dataset** (100-200 samples)
2. **Use fewer epochs** (1-2 instead of 3)
3. **Save checkpoints frequently**
4. **Resume training if interrupted**

## 📋 **Step-by-Step Colab Setup**

### 1. Open Google Colab
- Go to [colab.research.google.com](https://colab.research.google.com)
- Create new notebook

### 2. Enable GPU
```python
# Check GPU availability
import torch
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"GPU: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'None'}")
```

### 3. Upload Files
```python
from google.colab import files
# Upload your Python files
uploaded = files.upload()
```

### 4. Install Dependencies
```python
!pip install torch transformers datasets accelerate
```

### 5. Run Training
```python
!python fine_tune_dairy_llm.py
```

### 6. Download Trained Model
```python
from google.colab import files
import shutil

# Zip the model directory
shutil.make_archive('dairy-sql-model', 'zip', 'dairy-sql-model')
files.download('dairy-sql-model.zip')
```

## ⚡ **Quick Start Commands**

### Colab/Kaggle One-Liner:
```bash
# Clone, install, and run
!git clone <your-repo> && cd <repo-name> && pip install -r requirements.txt && python fine_tune_dairy_llm.py
```

### Local Development:
```bash
# Setup virtual environment
python -m venv dairy_llm_env
source dairy_llm_env/bin/activate  # Linux/Mac
# dairy_llm_env\Scripts\activate   # Windows

# Install and run
pip install -r requirements.txt
python dataset_generator.py
python fine_tune_dairy_llm.py
```

## 🎯 **Expected Results**

### Training Time:
- **CodeT5+ 220M**: 30-60 minutes
- **CodeT5+ 770M**: 1-2 hours
- **Larger models**: 2-4 hours

### Model Size:
- **Original**: ~1GB
- **Fine-tuned**: ~1.2GB
- **Quantized**: ~300MB (optional)

## 🔍 **Troubleshooting**

### Out of Memory (OOM):
```python
# Reduce batch size
per_device_train_batch_size=1
gradient_accumulation_steps=8

# Enable gradient checkpointing
gradient_checkpointing=True
```

### Slow Training:
```python
# Use mixed precision
fp16=True

# Reduce max sequence length
max_length=256  # instead of 512
```

### Session Timeout:
- Save checkpoints every 100 steps
- Use Google Colab Pro for longer sessions
- Resume from last checkpoint

## 📊 **Cost Comparison**

| Platform | Free Tier | GPU Type | Hours/Month | Cost/Hour |
|----------|-----------|----------|-------------|-----------|
| Google Colab | Yes | T4 | ~100 | Free |
| Colab Pro | $10/month | A100/V100 | ~200 | $0.05 |
| Kaggle | Yes | P100 | 120 | Free |
| Paperspace | Limited | Various | ~5 | $0.45+ |

**Recommendation**: Start with Google Colab free tier, upgrade to Pro if needed.
