#!/usr/bin/env python3
"""
Setup and Run Script for Dairy LLM Fine-tuning
Automates the entire process from dataset generation to model training
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    print(f"Running: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully!")
        if result.stdout:
            print(f"Output: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed!")
        print(f"Error: {e.stderr}")
        return False

def check_requirements():
    """Check if required packages are installed"""
    print("🔍 Checking requirements...")
    
    required_packages = [
        'torch', 'transformers', 'datasets', 'accelerate', 
        'numpy', 'pandas', 'tqdm'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            print(f"❌ {package} is missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        install_cmd = f"pip install {' '.join(missing_packages)}"
        return run_command(install_cmd, "Installing missing packages")
    
    return True

def generate_dataset():
    """Generate the training dataset"""
    print("\n📊 Generating training dataset...")
    
    try:
        from dataset_generator import DairyDatasetGenerator
        generator = DairyDatasetGenerator()
        generator.save_dataset("dairy_sql_dataset.json")
        
        # Check if dataset was created
        if os.path.exists("dairy_sql_dataset.json"):
            with open("dairy_sql_dataset.json", 'r') as f:
                data = json.load(f)
            print(f"✅ Dataset generated with {len(data)} samples")
            return True
        else:
            print("❌ Dataset file not found")
            return False
            
    except Exception as e:
        print(f"❌ Error generating dataset: {e}")
        return False

def train_model():
    """Train the fine-tuned model"""
    print("\n🚀 Starting model training...")
    print("This may take 30-60 minutes depending on your hardware...")
    
    try:
        from fine_tune_dairy_llm import DairyLLMFineTuner
        
        fine_tuner = DairyLLMFineTuner()
        fine_tuner.fine_tune("dairy_sql_dataset.json")
        
        # Check if model was saved
        if os.path.exists("dairy-sql-model"):
            print("✅ Model training completed successfully!")
            return True
        else:
            print("❌ Model directory not found")
            return False
            
    except Exception as e:
        print(f"❌ Error during training: {e}")
        return False

def test_model():
    """Test the trained model"""
    print("\n🧪 Testing the trained model...")
    
    try:
        from dairy_sql_assistant import DairySQLAssistant
        
        assistant = DairySQLAssistant("./dairy-sql-model")
        
        test_questions = [
            "How many sites do I have?",
            "How many sites does Sunrise Dairy Farm have?",
            "What is the average milk production?",
        ]
        
        print("Running test questions...")
        for question in test_questions:
            result = assistant.ask_question(question, execute=False)
            print(f"✅ Question: {question}")
            print(f"   SQL: {result['sql']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False

def main():
    """Main setup and run function"""
    print("🐄 DAIRY LLM FINE-TUNING SETUP")
    print("=" * 50)
    
    # Step 1: Check requirements
    if not check_requirements():
        print("❌ Failed to install requirements. Please install manually:")
        print("pip install -r requirements.txt")
        return
    
    # Step 2: Generate dataset
    if not generate_dataset():
        print("❌ Failed to generate dataset. Check dataset_generator.py")
        return
    
    # Step 3: Ask user about training
    print("\n🤔 Ready to start training!")
    print("Training will take 30-60 minutes and requires:")
    print("- 4GB+ GPU memory (or CPU training will be very slow)")
    print("- Stable internet connection (for downloading model)")
    
    response = input("\nDo you want to start training now? (y/n): ").lower().strip()
    
    if response not in ['y', 'yes']:
        print("\n📋 To train later, run:")
        print("python fine_tune_dairy_llm.py")
        print("\n📋 To use pre-trained model:")
        print("python dairy_sql_assistant.py --interactive")
        return
    
    # Step 4: Train model
    if not train_model():
        print("❌ Training failed. Check the error messages above.")
        print("💡 Try using Google Colab for free GPU training!")
        return
    
    # Step 5: Test model
    if not test_model():
        print("❌ Model testing failed.")
        return
    
    # Success!
    print("\n🎉 SUCCESS! Your dairy LLM is ready!")
    print("=" * 50)
    print("📁 Model saved to: ./dairy-sql-model")
    print("📊 Dataset saved to: dairy_sql_dataset.json")
    
    print("\n🚀 Next steps:")
    print("1. Test interactively: python dairy_sql_assistant.py --interactive")
    print("2. Connect to database: python dairy_sql_assistant.py --database your_db.db --interactive")
    print("3. Ask single question: python dairy_sql_assistant.py --question 'How many sites do I have?'")
    
    # Ask if user wants to try interactive mode
    response = input("\nWould you like to try the interactive mode now? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        print("\n🚀 Starting interactive mode...")
        try:
            from dairy_sql_assistant import DairySQLAssistant
            assistant = DairySQLAssistant("./dairy-sql-model")
            assistant.interactive_mode()
        except Exception as e:
            print(f"❌ Error starting interactive mode: {e}")

def quick_demo():
    """Run a quick demo without training"""
    print("🚀 QUICK DEMO MODE")
    print("=" * 30)
    print("This will generate dataset and show examples without training.")
    
    if not check_requirements():
        return
    
    if not generate_dataset():
        return
    
    print("\n📊 Dataset generated! Here are some examples:")
    
    try:
        with open("dairy_sql_dataset.json", 'r') as f:
            data = json.load(f)
        
        for i, sample in enumerate(data[:5]):
            print(f"\n{i+1}. Question: {sample['input']}")
            print(f"   SQL: {sample['output']}")
    
    except Exception as e:
        print(f"❌ Error reading dataset: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        quick_demo()
    else:
        main()
