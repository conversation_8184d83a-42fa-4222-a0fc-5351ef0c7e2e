#!/usr/bin/env python3
"""
Fine-tuning Script for Dairy Database LLM
Fine-tunes CodeT5+ model for text-to-SQL tasks on dairy farm data
"""

import json
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import (
    AutoTokenizer, 
    AutoModelForSeq2SeqLM,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq
)
from datasets import Dataset as HFDataset
import numpy as np
from typing import Dict, List
import os

class DairySQLDataset(Dataset):
    def __init__(self, data: List[Dict], tokenizer, max_length: int = 512):
        self.data = data
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        
        # Create input text with instruction and question
        input_text = f"{item['instruction']}\nQuestion: {item['input']}\nSQL:"
        target_text = item['output']
        
        # Tokenize input
        input_encoding = self.tokenizer(
            input_text,
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        # Tokenize target
        target_encoding = self.tokenizer(
            target_text,
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        return {
            'input_ids': input_encoding['input_ids'].flatten(),
            'attention_mask': input_encoding['attention_mask'].flatten(),
            'labels': target_encoding['input_ids'].flatten()
        }

class DairyLLMFineTuner:
    def __init__(self, model_name: str = "Salesforce/codet5p-220m"):
        self.model_name = model_name
        self.tokenizer = None
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
    
    def load_model_and_tokenizer(self):
        """Load the pre-trained model and tokenizer"""
        print(f"Loading model: {self.model_name}")
        
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        self.model = AutoModelForSeq2SeqLM.from_pretrained(self.model_name)
        
        # Add special tokens if needed
        special_tokens = {
            "pad_token": "<pad>",
            "eos_token": "</s>",
            "bos_token": "<s>",
        }
        
        self.tokenizer.add_special_tokens(special_tokens)
        self.model.resize_token_embeddings(len(self.tokenizer))
        
        print(f"Model loaded successfully. Vocab size: {len(self.tokenizer)}")
    
    def load_dataset(self, dataset_path: str):
        """Load and prepare the training dataset"""
        print(f"Loading dataset from: {dataset_path}")
        
        with open(dataset_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Split into train/validation (80/20)
        split_idx = int(0.8 * len(data))
        train_data = data[:split_idx]
        val_data = data[split_idx:]
        
        print(f"Training samples: {len(train_data)}")
        print(f"Validation samples: {len(val_data)}")
        
        return train_data, val_data
    
    def prepare_datasets(self, train_data: List[Dict], val_data: List[Dict]):
        """Prepare datasets for training"""
        def preprocess_function(examples):
            inputs = []
            targets = []
            
            for i in range(len(examples['input'])):
                input_text = f"{examples['instruction'][i]}\nQuestion: {examples['input'][i]}\nSQL:"
                inputs.append(input_text)
                targets.append(examples['output'][i])
            
            model_inputs = self.tokenizer(
                inputs, 
                max_length=512, 
                truncation=True, 
                padding=True
            )
            
            # Setup the tokenizer for targets
            with self.tokenizer.as_target_tokenizer():
                labels = self.tokenizer(
                    targets, 
                    max_length=512, 
                    truncation=True, 
                    padding=True
                )
            
            model_inputs["labels"] = labels["input_ids"]
            return model_inputs
        
        # Convert to HuggingFace datasets
        train_dataset = HFDataset.from_list(train_data)
        val_dataset = HFDataset.from_list(val_data)
        
        # Apply preprocessing
        train_dataset = train_dataset.map(preprocess_function, batched=True)
        val_dataset = val_dataset.map(preprocess_function, batched=True)
        
        return train_dataset, val_dataset
    
    def fine_tune(self, dataset_path: str, output_dir: str = "./dairy-sql-model"):
        """Fine-tune the model"""
        # Load model and tokenizer
        self.load_model_and_tokenizer()
        
        # Load and prepare data
        train_data, val_data = self.load_dataset(dataset_path)
        train_dataset, val_dataset = self.prepare_datasets(train_data, val_data)
        
        # Training arguments
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=3,
            per_device_train_batch_size=4,
            per_device_eval_batch_size=4,
            warmup_steps=100,
            weight_decay=0.01,
            logging_dir=f'{output_dir}/logs',
            logging_steps=10,
            evaluation_strategy="steps",
            eval_steps=50,
            save_steps=100,
            save_total_limit=2,
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            report_to=None,  # Disable wandb/tensorboard
            dataloader_pin_memory=False,
        )
        
        # Data collator
        data_collator = DataCollatorForSeq2Seq(
            tokenizer=self.tokenizer,
            model=self.model,
            padding=True
        )
        
        # Initialize trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            tokenizer=self.tokenizer,
            data_collator=data_collator,
        )
        
        # Start training
        print("Starting fine-tuning...")
        trainer.train()
        
        # Save the final model
        trainer.save_model()
        self.tokenizer.save_pretrained(output_dir)
        
        print(f"Model saved to: {output_dir}")
    
    def test_model(self, model_path: str, test_questions: List[str]):
        """Test the fine-tuned model"""
        print(f"Loading fine-tuned model from: {model_path}")
        
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModelForSeq2SeqLM.from_pretrained(model_path)
        model.to(self.device)
        model.eval()
        
        print("\nTesting the model:")
        print("=" * 50)
        
        for question in test_questions:
            input_text = f"Generate SQL query for the following question about dairy farm data:\nQuestion: {question}\nSQL:"
            
            inputs = tokenizer(input_text, return_tensors="pt", max_length=512, truncation=True)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_length=256,
                    num_beams=4,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=tokenizer.pad_token_id
                )
            
            generated_sql = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            print(f"\nQuestion: {question}")
            print(f"Generated SQL: {generated_sql}")
            print("-" * 30)

def main():
    # Initialize fine-tuner
    fine_tuner = DairyLLMFineTuner()
    
    # Generate dataset first
    print("Generating training dataset...")
    from dataset_generator import DairyDatasetGenerator
    generator = DairyDatasetGenerator()
    generator.save_dataset("dairy_sql_dataset.json")
    
    # Fine-tune the model
    print("\nStarting fine-tuning process...")
    fine_tuner.fine_tune("dairy_sql_dataset.json")
    
    # Test the model
    test_questions = [
        "How many sites do I have?",
        "How many sites does Sunrise Dairy Farm have?",
        "What data is inside visit 'Health Check'?",
        "What is the average milk production?",
        "Show me all customers in Canada"
    ]
    
    fine_tuner.test_model("./dairy-sql-model", test_questions)

if __name__ == "__main__":
    main()
